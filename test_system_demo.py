#!/usr/bin/env python3
"""
Comprehensive System Testing and Demonstration Script
Tests all components of the foot deformity detection system
"""

import requests
import json
import os
import time
from pathlib import Path

class FootDeformitySystemTester:
    def __init__(self, api_base_url="http://localhost:8000"):
        self.api_base_url = api_base_url
        self.test_images_dir = "ml_model/processed_dataset/test"
        
    def test_api_health(self):
        """Test API health endpoint"""
        print("🏥 Testing API Health...")
        try:
            response = requests.get(f"{self.api_base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API is healthy: {data}")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API connection failed: {e}")
            return False
    
    def test_model_info(self):
        """Test model information endpoint"""
        print("\n🧠 Testing Model Info...")
        try:
            response = requests.get(f"{self.api_base_url}/model-info")
            if response.status_code == 200:
                data = response.json()
                print("✅ Model Information:")
                print(f"   - Model loaded: {data.get('model_loaded')}")
                print(f"   - Input shape: {data.get('input_shape')}")
                print(f"   - Output shape: {data.get('output_shape')}")
                print(f"   - Total parameters: {data.get('total_parameters'):,}")
                print(f"   - Classes: {data.get('class_names')}")
                return True
            else:
                print(f"❌ Model info failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Model info request failed: {e}")
            return False
    
    def test_prediction_endpoint(self, image_path, expected_class=None):
        """Test prediction endpoint with a specific image"""
        print(f"\n🔍 Testing Prediction: {os.path.basename(image_path)}")
        
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return False
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
                response = requests.post(f"{self.api_base_url}/predict", files=files)
            
            if response.status_code == 200:
                data = response.json()
                prediction = data['prediction']
                probabilities = data['probabilities']
                
                print("✅ Prediction Results:")
                print(f"   - Predicted class: {prediction['class']}")
                print(f"   - Confidence: {prediction['confidence']:.4f}")
                print(f"   - Severity: {prediction['severity']}")
                print(f"   - Recommendation: {prediction['recommendation']}")
                print("   - All probabilities:")
                for class_name, prob in probabilities.items():
                    print(f"     • {class_name}: {prob:.4f}")
                
                if expected_class:
                    is_correct = prediction['class'] == expected_class
                    status = "✅ CORRECT" if is_correct else "❌ INCORRECT"
                    print(f"   - Expected: {expected_class} | Result: {status}")
                
                return True
            else:
                print(f"❌ Prediction failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Prediction request failed: {e}")
            return False
    
    def test_all_sample_images(self):
        """Test predictions on all available sample images"""
        print("\n📸 Testing All Sample Images...")
        
        if not os.path.exists(self.test_images_dir):
            print(f"❌ Test images directory not found: {self.test_images_dir}")
            return False
        
        results = {}
        total_tests = 0
        correct_predictions = 0
        
        # Test each class
        for class_name in ['normal', 'flatfoot', 'hallux_valgus']:
            class_dir = os.path.join(self.test_images_dir, class_name)
            if os.path.exists(class_dir):
                images = [f for f in os.listdir(class_dir) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                print(f"\n📁 Testing {class_name} images ({len(images)} files):")
                class_results = []
                
                for image_file in images[:3]:  # Test first 3 images per class
                    image_path = os.path.join(class_dir, image_file)
                    
                    # Make prediction
                    try:
                        with open(image_path, 'rb') as f:
                            files = {'file': (image_file, f, 'image/jpeg')}
                            response = requests.post(f"{self.api_base_url}/predict", files=files)
                        
                        if response.status_code == 200:
                            data = response.json()
                            predicted_class = data['prediction']['class']
                            confidence = data['prediction']['confidence']
                            
                            is_correct = predicted_class == class_name
                            if is_correct:
                                correct_predictions += 1
                            
                            status = "✅" if is_correct else "❌"
                            print(f"   {status} {image_file}: {predicted_class} ({confidence:.3f})")
                            
                            class_results.append({
                                'file': image_file,
                                'predicted': predicted_class,
                                'confidence': confidence,
                                'correct': is_correct
                            })
                            
                            total_tests += 1
                            
                        else:
                            print(f"   ❌ {image_file}: API error {response.status_code}")
                            
                    except Exception as e:
                        print(f"   ❌ {image_file}: {e}")
                
                results[class_name] = class_results
        
        # Summary
        accuracy = (correct_predictions / total_tests * 100) if total_tests > 0 else 0
        print(f"\n📊 Test Summary:")
        print(f"   - Total tests: {total_tests}")
        print(f"   - Correct predictions: {correct_predictions}")
        print(f"   - Accuracy: {accuracy:.2f}%")
        
        return results
    
    def test_error_handling(self):
        """Test API error handling"""
        print("\n🚨 Testing Error Handling...")
        
        # Test with invalid file
        print("   Testing invalid file upload...")
        try:
            files = {'file': ('test.txt', 'This is not an image', 'text/plain')}
            response = requests.post(f"{self.api_base_url}/predict", files=files)
            print(f"   Response status: {response.status_code}")
            if response.status_code != 200:
                print("   ✅ Correctly rejected invalid file")
            else:
                print("   ⚠️ API accepted invalid file")
        except Exception as e:
            print(f"   ❌ Error testing invalid file: {e}")
        
        # Test with no file
        print("   Testing request with no file...")
        try:
            response = requests.post(f"{self.api_base_url}/predict")
            print(f"   Response status: {response.status_code}")
            if response.status_code != 200:
                print("   ✅ Correctly rejected empty request")
            else:
                print("   ⚠️ API accepted empty request")
        except Exception as e:
            print(f"   ❌ Error testing empty request: {e}")
    
    def run_full_demo(self):
        """Run complete system demonstration"""
        print("🎯 FOOT DEFORMITY DETECTION SYSTEM - FULL DEMO")
        print("="*60)
        
        # Test API health
        if not self.test_api_health():
            print("❌ API is not available. Please start the API server first.")
            return False
        
        # Test model info
        self.test_model_info()
        
        # Test sample predictions
        sample_images = [
            ("ml_model/processed_dataset/test/normal/flatfoot_Flatfoot13_jpg.rf.4148db8039e51d129a38c2eaa88db058.jpg", "normal"),
            ("ml_model/processed_dataset/test/hallux_valgus/hv_HV3-48-ARR-_jpg.rf.6c3b93ec4966aca032bc23bd7c08361f.jpg", "hallux_valgus"),
            ("ml_model/processed_dataset/test/flatfoot/flatfoot_Flatfoot14_jpg.rf.cb566d841627a53a540d5a5fee993c50.jpg", "flatfoot")
        ]
        
        for image_path, expected_class in sample_images:
            if os.path.exists(image_path):
                self.test_prediction_endpoint(image_path, expected_class)
        
        # Test all sample images
        self.test_all_sample_images()
        
        # Test error handling
        self.test_error_handling()
        
        print("\n" + "="*60)
        print("🎉 DEMO COMPLETE!")
        print("="*60)
        print("System components tested:")
        print("✅ API Health Check")
        print("✅ Model Information")
        print("✅ Image Prediction")
        print("✅ Error Handling")
        print("✅ Batch Testing")
        
        return True

def main():
    """Main demonstration function"""
    tester = FootDeformitySystemTester()
    tester.run_full_demo()

if __name__ == "__main__":
    main()
