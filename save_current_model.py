#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually save the current training model state.
This is needed because the ModelCheckpoint callback is not working properly
due to validation data issues.
"""

import os
import sys
import tensorflow as tf
from datetime import datetime

def save_current_model():
    """
    This script should be run when the training is showing good performance
    but the ModelCheckpoint is not saving due to validation issues.
    """
    
    # Check if we're in the right directory
    if not os.path.exists('ml_model'):
        print("Error: Please run this script from the project root directory")
        return False
    
    # Look for the training script process or saved model
    saved_models_dir = 'ml_model/saved_models'
    
    if not os.path.exists(saved_models_dir):
        print(f"Error: {saved_models_dir} directory not found")
        return False
    
    # Check current model
    current_model_path = os.path.join(saved_models_dir, 'best_model.keras')
    
    if os.path.exists(current_model_path):
        # Get file modification time
        mod_time = os.path.getmtime(current_model_path)
        mod_datetime = datetime.fromtimestamp(mod_time)
        print(f"Current model was saved at: {mod_datetime}")
        
        # Load and test the current model
        try:
            model = tf.keras.models.load_model(current_model_path)
            print(f"Model loaded successfully")
            print(f"Model input shape: {model.input_shape}")
            print(f"Model output shape: {model.output_shape}")
            print(f"Total parameters: {model.count_params():,}")
            
            # Test with a dummy input
            import numpy as np
            dummy_input = np.random.random((1, 224, 224, 3))
            prediction = model.predict(dummy_input, verbose=0)
            print(f"Test prediction shape: {prediction.shape}")
            print(f"Test prediction probabilities: {prediction[0]}")
            
            # Check if this looks like a reasonable model
            # (not predicting only one class with 99.99% confidence)
            max_prob = np.max(prediction[0])
            if max_prob > 0.999:
                print("⚠️  Warning: Model seems to be overfitting (very high confidence)")
                print("   This might be the problematic early model from epoch 2")
                return False
            else:
                print("✅ Model seems to have reasonable confidence distribution")
                return True
                
        except Exception as e:
            print(f"Error loading model: {e}")
            return False
    else:
        print(f"No model found at {current_model_path}")
        return False

def create_backup_and_wait_for_better_model():
    """
    Create a backup of the current model and wait for training to produce a better one.
    """
    saved_models_dir = 'ml_model/saved_models'
    current_model_path = os.path.join(saved_models_dir, 'best_model.keras')
    backup_model_path = os.path.join(saved_models_dir, 'backup_epoch2_model.keras')
    
    if os.path.exists(current_model_path):
        # Create backup of the problematic model
        import shutil
        shutil.copy2(current_model_path, backup_model_path)
        print(f"✅ Backed up current model to {backup_model_path}")
        
        print("\n" + "="*60)
        print("RECOMMENDATION:")
        print("="*60)
        print("The current model (from epoch 2) is overfitting and predicting")
        print("only 'normal' class with 99.99% confidence.")
        print("")
        print("The training is progressing well (90%+ accuracy in epoch 9),")
        print("but the ModelCheckpoint is not saving due to validation issues.")
        print("")
        print("OPTIONS:")
        print("1. Wait for training to complete and manually copy the final model")
        print("2. Modify the training script to save models more frequently")
        print("3. Stop training and restart with fixed validation data")
        print("")
        print("Current training shows excellent progress:")
        print("- Epoch 9: 90.07% accuracy, 0.28 loss")
        print("- Much better than the saved model from epoch 2")
        print("="*60)
        
        return True
    else:
        print("No current model found to backup")
        return False

def main():
    print("="*60)
    print("MODEL DIAGNOSIS AND BACKUP TOOL")
    print("="*60)
    
    # Check current model status
    if save_current_model():
        print("✅ Current model seems reasonable - no action needed")
    else:
        print("⚠️  Current model has issues - creating backup and recommendations")
        create_backup_and_wait_for_better_model()

if __name__ == "__main__":
    main()
