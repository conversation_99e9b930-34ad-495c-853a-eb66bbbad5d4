# Foot Deformity Detection API Documentation

## Overview

The Foot Deformity Detection API is a RESTful service built with FastAPI that provides machine learning-based classification of foot deformities. The API can detect and classify three conditions: normal feet, flatfoot, and hallux valgus (bunions).

## Base URL
```
http://localhost:8000
```

## Authentication
Currently, no authentication is required for API access.

## Endpoints

### 1. Root Endpoint
**GET** `/`

Returns basic API information and available endpoints.

**Response:**
```json
{
  "message": "Foot Deformity Detection API",
  "version": "1.0.0",
  "status": "running",
  "model_loaded": true,
  "endpoints": {
    "predict": "/predict",
    "health": "/health",
    "model-info": "/model-info"
  }
}
```

### 2. Health Check
**GET** `/health`

Returns the current health status of the API service.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000000",
  "model_loaded": true
}
```

**Status Codes:**
- `200`: Service is healthy
- `503`: Service unavailable

### 3. Model Information
**GET** `/model-info`

Returns detailed information about the loaded machine learning model.

**Response:**
```json
{
  "model_loaded": true,
  "input_shape": [null, 224, 224, 3],
  "output_shape": [null, 3],
  "total_parameters": 1440931,
  "class_names": ["flatfoot", "hallux_valgus", "normal"],
  "num_classes": 3
}
```

**Status Codes:**
- `200`: Model information retrieved successfully
- `503`: Model not loaded

### 4. Single Image Prediction
**POST** `/predict`

Analyzes a single foot image and returns classification results.

**Request:**
- **Content-Type**: `multipart/form-data`
- **Body**: Form data with image file

**Parameters:**
- `file` (required): Image file (JPEG, PNG, BMP, TIFF)

**Example Request:**
```bash
curl -X POST "http://localhost:8000/predict" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@foot_image.jpg"
```

**Response:**
```json
{
  "success": true,
  "prediction": {
    "class": "normal",
    "confidence": 0.9999585151672363,
    "severity": "Normal",
    "recommendation": "No deformity detected. Maintain good foot health."
  },
  "probabilities": {
    "flatfoot": 0.00003220892540412024,
    "hallux_valgus": 0.000009250400580640417,
    "normal": 0.9999585151672363
  },
  "metadata": {
    "filename": "foot_image.jpg",
    "image_size": "224x224",
    "timestamp": "2024-01-01T12:00:00.000000"
  }
}
```

**Status Codes:**
- `200`: Prediction successful
- `400`: Invalid file format or corrupted image
- `413`: File too large
- `503`: Model not loaded

### 5. Batch Prediction
**POST** `/batch-predict`

Analyzes multiple foot images in a single request.

**Request:**
- **Content-Type**: `multipart/form-data`
- **Body**: Form data with multiple image files

**Parameters:**
- `files` (required): Array of image files (max 10 files)

**Example Request:**
```bash
curl -X POST "http://localhost:8000/batch-predict" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "files=@image1.jpg" \
     -F "files=@image2.jpg"
```

**Response:**
```json
{
  "success": true,
  "results": [
    {
      "filename": "image1.jpg",
      "success": true,
      "prediction": {
        "class": "normal",
        "confidence": 0.95
      },
      "probabilities": {
        "flatfoot": 0.02,
        "hallux_valgus": 0.03,
        "normal": 0.95
      }
    },
    {
      "filename": "image2.jpg",
      "success": false,
      "error": "Invalid image format"
    }
  ],
  "total_processed": 2,
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## Data Models

### Prediction Result
```json
{
  "class": "string",           // Predicted class name
  "confidence": "number",      // Confidence score (0-1)
  "severity": "string",        // Severity level
  "recommendation": "string"   // Medical recommendation
}
```

### Class Information
- **normal**: Healthy foot structure
- **flatfoot**: Collapsed arch condition (pes planus)
- **hallux_valgus**: Bunion condition

### Severity Levels
- **Normal**: No deformity detected
- **Mild to Moderate**: Minor to moderate deformity
- **Mild to Severe**: Significant deformity requiring attention

## Error Handling

### Error Response Format
```json
{
  "detail": "Error message description",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### Common Error Codes
- `400`: Bad Request - Invalid input data
- `413`: Payload Too Large - File size exceeds limit
- `415`: Unsupported Media Type - Invalid file format
- `503`: Service Unavailable - Model not loaded
- `500`: Internal Server Error - Unexpected server error

## File Requirements

### Supported Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff)

### File Size Limits
- Maximum file size: 10MB per image
- Maximum batch size: 10 images per request

### Image Requirements
- Minimum resolution: 224x224 pixels
- Color images preferred (RGB)
- Clear, well-lit foot images
- Minimal background noise

## Rate Limiting
Currently, no rate limiting is implemented. For production use, consider implementing appropriate rate limiting based on your requirements.

## CORS Policy
The API currently allows all origins (`*`). For production deployment, configure specific allowed origins in the CORS middleware.

## Example Usage

### Python Example
```python
import requests

# Single prediction
with open('foot_image.jpg', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8000/predict', files=files)
    result = response.json()
    print(f"Prediction: {result['prediction']['class']}")
    print(f"Confidence: {result['prediction']['confidence']:.2%}")
```

### JavaScript Example
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('http://localhost:8000/predict', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('Prediction:', data.prediction.class);
    console.log('Confidence:', data.prediction.confidence);
});
```

## Performance Considerations

### Response Times
- Single prediction: ~1-2 seconds
- Batch prediction: ~1-2 seconds per image
- Model loading: ~5-10 seconds on startup

### Optimization Tips
- Use appropriate image sizes (224x224 recommended)
- Compress images before upload
- Use batch prediction for multiple images
- Cache model in memory for repeated use

## Medical Disclaimer

This API is designed to assist medical professionals and should not be used as a substitute for professional medical diagnosis. Always consult with a qualified healthcare provider for proper medical evaluation and treatment.
