# Foot Deformity Detection System - Setup Guide

This guide will help you set up and run the complete Foot Deformity Detection System on your local machine.

## System Requirements

### Hardware Requirements
- **CPU**: Multi-core processor (Intel i5/AMD Ryzen 5 or better recommended)
- **RAM**: Minimum 8GB, 16GB recommended for training
- **Storage**: At least 5GB free space
- **GPU**: Optional but recommended for faster training (CUDA-compatible)

### Software Requirements
- **Python**: 3.8 or higher
- **Node.js**: 16 or higher (for frontend development)
- **npm**: 7 or higher
- **Git**: For version control

## Installation Steps

### 1. Clone the Repository
```bash
git clone <repository-url>
cd foot-deformity-detection
```

### 2. Set Up Python Environment

#### Option A: Using Virtual Environment (Recommended)
```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate
```

#### Option B: Using Conda
```bash
conda create -n foot-detection python=3.10
conda activate foot-detection
```

### 3. Install Python Dependencies

#### For Machine Learning Components
```bash
cd ml_model
pip install -r requirements.txt
```

#### For API Backend
```bash
cd ../api
pip install -r requirements.txt
```

### 4. Install Frontend Dependencies
```bash
cd ../frontend
npm install
```

## Dataset Setup

The project comes with pre-processed datasets, but you can also set up your own:

### Using Existing Dataset
The processed dataset is already available in `ml_model/processed_dataset/` with the following structure:
```
processed_dataset/
├── train/
│   ├── normal/
│   ├── flatfoot/
│   └── hallux_valgus/
├── validation/
│   ├── normal/
│   ├── flatfoot/
│   └── hallux_valgus/
└── test/
    ├── normal/
    ├── flatfoot/
    └── hallux_valgus/
```

### Dataset Statistics
- **Training Set**: 1,152 images
- **Validation Set**: 69 images  
- **Test Set**: 30 images
- **Classes**: Normal (288), Flatfoot (255), Hallux Valgus (609)

## Model Training

### Quick Start (Use Pre-trained Model)
A pre-trained model is available in `ml_model/saved_models/`. Skip to the "Running the Application" section if you want to use it directly.

### Training Your Own Model
```bash
cd ml_model
python training/train_model.py
```

**Training Configuration:**
- **Epochs**: 30
- **Batch Size**: 16
- **Learning Rate**: 0.001
- **Architecture**: Custom CNN with 1.44M parameters

**Expected Training Time:**
- **CPU**: 2-4 hours
- **GPU**: 30-60 minutes

### Monitoring Training Progress
The training script will:
1. Display real-time progress with accuracy and loss metrics
2. Save the best model to `ml_model/saved_models/best_model.keras`
3. Generate evaluation plots in `ml_model/evaluation/`
4. Create training logs in `ml_model/training/training_log.csv`

## Running the Application

### 1. Start the API Server
```bash
cd api
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at: `http://localhost:8000`

### 2. Start the Frontend (Development)

#### Option A: React Development Server (if Node.js compatible)
```bash
cd frontend
npm run dev
```

#### Option B: Static HTML Demo
Open `frontend/demo.html` in your web browser:
```bash
# On macOS
open frontend/demo.html

# On Linux
xdg-open frontend/demo.html

# On Windows
start frontend/demo.html
```

### 3. Verify Installation
Test the API endpoints:
```bash
# Health check
curl http://localhost:8000/health

# Model information
curl http://localhost:8000/model-info

# Test prediction (replace with actual image path)
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@path/to/test/image.jpg"
```

## Usage Instructions

### Web Interface
1. Open the frontend application in your browser
2. Click or drag-and-drop a foot image into the upload area
3. Click "Analyze Image" to get predictions
4. View results including:
   - Predicted class (Normal, Flatfoot, Hallux Valgus)
   - Confidence score
   - Detailed probabilities for each class
   - Medical recommendations

### API Usage
The REST API provides the following endpoints:

#### GET `/health`
Health check endpoint
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "model_loaded": true
}
```

#### GET `/model-info`
Model information
```json
{
  "model_loaded": true,
  "input_shape": [null, 224, 224, 3],
  "total_parameters": 1440931,
  "class_names": ["flatfoot", "hallux_valgus", "normal"],
  "num_classes": 3
}
```

#### POST `/predict`
Image prediction
- **Input**: Multipart form data with image file
- **Output**: JSON with prediction results

## Troubleshooting

### Common Issues

#### 1. Model Not Loading
**Error**: "Model not loaded" in API responses
**Solution**: 
- Ensure the model file exists in `ml_model/saved_models/`
- Check API logs for specific error messages
- Verify TensorFlow installation

#### 2. CORS Issues
**Error**: Frontend cannot connect to API
**Solution**: 
- Ensure API is running on `http://localhost:8000`
- Check browser console for CORS errors
- Verify API CORS settings in `api/app/main.py`

#### 3. Memory Issues During Training
**Error**: Out of memory during training
**Solution**:
- Reduce batch size in training script
- Close other applications
- Use GPU if available

#### 4. Frontend Not Loading
**Error**: React development server issues
**Solution**:
- Use the static HTML demo (`frontend/demo.html`)
- Check Node.js version compatibility
- Clear npm cache: `npm cache clean --force`

### Performance Optimization

#### For Training
- Use GPU acceleration if available
- Increase batch size if you have more RAM
- Use mixed precision training for faster convergence

#### For Inference
- Use model quantization for faster predictions
- Implement batch prediction for multiple images
- Cache model in memory for repeated use

## Next Steps

1. **Model Improvement**: Collect more diverse training data
2. **Deployment**: Set up production deployment with Docker
3. **Monitoring**: Implement logging and monitoring systems
4. **Security**: Add authentication and input validation
5. **Scaling**: Implement load balancing for high traffic

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the API documentation
3. Check training logs and evaluation metrics
4. Ensure all dependencies are correctly installed

## Medical Disclaimer

This system is designed to assist medical professionals and should not be used as a substitute for professional medical diagnosis. Always consult with a qualified healthcare provider for proper medical evaluation and treatment.
