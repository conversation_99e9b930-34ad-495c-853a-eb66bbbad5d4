# Deployment Guide - Foot Deformity Detection System

This guide covers deployment options for the Foot Deformity Detection System in production environments.

## Deployment Options

### 1. Local Development Deployment
For testing and development purposes.

#### Prerequisites
- Python 3.8+
- 8GB+ RAM
- 5GB+ storage

#### Steps
```bash
# Clone repository
git clone <repository-url>
cd foot-deformity-detection

# Install dependencies
cd ml_model && pip install -r requirements.txt
cd ../api && pip install -r requirements.txt

# Start API server
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# Access frontend
open frontend/demo.html
```

### 2. Docker Deployment (Recommended)

#### Create Dockerfile for API
```dockerfile
# api/Dockerfile
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy model files
COPY ../ml_model/saved_models ./ml_model/saved_models

# Expose port
EXPOSE 8000

# Run application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Create Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: ./api
    ports:
      - "8000:8000"
    volumes:
      - ./ml_model/saved_models:/app/ml_model/saved_models:ro
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
    depends_on:
      - api
    restart: unless-stopped
```

#### Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f api
```

### 3. Cloud Deployment

#### AWS Deployment

##### Option A: AWS EC2
```bash
# Launch EC2 instance (t3.medium or larger)
# Install Docker
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Deploy application
git clone <repository-url>
cd foot-deformity-detection
docker-compose up -d
```

##### Option B: AWS ECS (Fargate)
```yaml
# ecs-task-definition.json
{
  "family": "foot-deformity-detection",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "api",
      "image": "your-account.dkr.ecr.region.amazonaws.com/foot-deformity-api:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/foot-deformity-detection",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Google Cloud Platform

##### Cloud Run Deployment
```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/foot-deformity-api

# Deploy to Cloud Run
gcloud run deploy foot-deformity-api \
  --image gcr.io/PROJECT-ID/foot-deformity-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 1
```

#### Azure Deployment

##### Container Instances
```bash
# Create resource group
az group create --name foot-deformity-rg --location eastus

# Create container instance
az container create \
  --resource-group foot-deformity-rg \
  --name foot-deformity-api \
  --image your-registry/foot-deformity-api:latest \
  --cpu 1 \
  --memory 2 \
  --ports 8000 \
  --dns-name-label foot-deformity-unique
```

### 4. Kubernetes Deployment

#### Kubernetes Manifests
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: foot-deformity-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: foot-deformity-api
  template:
    metadata:
      labels:
        app: foot-deformity-api
    spec:
      containers:
      - name: api
        image: foot-deformity-api:latest
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: foot-deformity-service
spec:
  selector:
    app: foot-deformity-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

#### Deploy to Kubernetes
```bash
# Apply manifests
kubectl apply -f k8s/

# Check deployment
kubectl get pods
kubectl get services

# Scale deployment
kubectl scale deployment foot-deformity-api --replicas=5
```

## Production Considerations

### Security
```python
# Add to api/app/main.py
from fastapi.middleware.trustedhost import TrustedHostMiddleware

app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["yourdomain.com", "*.yourdomain.com"]
)

# Add rate limiting
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.post("/predict")
@limiter.limit("10/minute")
async def predict_deformity(request: Request, file: UploadFile = File(...)):
    # ... existing code
```

### Monitoring
```yaml
# monitoring/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

### Load Balancing
```nginx
# nginx.conf
upstream api_backend {
    server api1:8000;
    server api2:8000;
    server api3:8000;
}

server {
    listen 80;
    server_name yourdomain.com;

    location /api/ {
        proxy_pass http://api_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        root /usr/share/nginx/html;
        index index.html;
    }
}
```

### Environment Variables
```bash
# .env
MODEL_PATH=/app/ml_model/saved_models/best_model.keras
MAX_FILE_SIZE=10485760
CORS_ORIGINS=https://yourdomain.com
LOG_LEVEL=INFO
```

### Health Checks
```python
# Enhanced health check
@app.get("/health")
async def health_check():
    checks = {
        "api": "healthy",
        "model": model is not None,
        "memory": psutil.virtual_memory().percent < 90,
        "disk": psutil.disk_usage('/').percent < 90
    }
    
    status = "healthy" if all(checks.values()) else "unhealthy"
    return {"status": status, "checks": checks}
```

## Performance Optimization

### Model Optimization
```python
# Convert to TensorFlow Lite for faster inference
import tensorflow as tf

converter = tf.lite.TFLiteConverter.from_saved_model('ml_model/saved_models/best_model.keras')
converter.optimizations = [tf.lite.Optimize.DEFAULT]
tflite_model = converter.convert()

with open('model.tflite', 'wb') as f:
    f.write(tflite_model)
```

### Caching
```python
# Add Redis caching
import redis
import hashlib

redis_client = redis.Redis(host='redis', port=6379, db=0)

async def predict_with_cache(image_data):
    # Create hash of image data
    image_hash = hashlib.md5(image_data).hexdigest()
    
    # Check cache
    cached_result = redis_client.get(f"prediction:{image_hash}")
    if cached_result:
        return json.loads(cached_result)
    
    # Make prediction
    result = await make_prediction(image_data)
    
    # Cache result (expire in 1 hour)
    redis_client.setex(f"prediction:{image_hash}", 3600, json.dumps(result))
    
    return result
```

## Backup and Recovery

### Database Backup (if using database)
```bash
# Backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U username dbname > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### Model Versioning
```bash
# Model versioning strategy
ml_model/saved_models/
├── v1.0/
│   ├── best_model.keras
│   └── metadata.json
├── v1.1/
│   ├── best_model.keras
│   └── metadata.json
└── current -> v1.1/
```

## Troubleshooting

### Common Issues
1. **High Memory Usage**: Reduce batch size, implement model quantization
2. **Slow Response Times**: Add caching, optimize model, use GPU
3. **Model Loading Errors**: Check file permissions, verify model format
4. **CORS Issues**: Configure proper CORS settings for production domain

### Monitoring Commands
```bash
# Check container resources
docker stats

# View application logs
docker-compose logs -f api

# Monitor system resources
htop
iostat -x 1
```

This deployment guide provides multiple options for deploying the Foot Deformity Detection System in various environments, from local development to production cloud deployments.
