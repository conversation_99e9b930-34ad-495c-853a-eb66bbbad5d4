# Foot Deformity Detection System

A comprehensive web application for detecting foot deformities using deep learning, specifically focusing on hallux valgus (bunions) and flatfoot conditions.

## Project Structure

```
foot-deformity-detection/
├── Dataset/                    # Original dataset files
│   ├── Flatfoot/              # Flatfoot detection datasets
│   └── Hallux Valgus/         # Hallux valgus datasets
├── ml_model/                  # Machine Learning components
│   ├── data_preprocessing/    # Data preparation scripts
│   ├── models/               # CNN model definitions
│   ├── training/             # Training scripts and utilities
│   ├── evaluation/           # Model evaluation and metrics
│   └── saved_models/         # Trained model files
├── api/                      # FastAPI backend
│   ├── app/                  # Main application code
│   ├── models/               # API data models
│   ├── routes/               # API endpoints
│   └── utils/                # Utility functions
├── frontend/                 # React frontend
│   ├── src/                  # Source code
│   ├── public/               # Static assets
│   └── components/           # React components
└── docs/                     # Documentation
```

## Features

- **Machine Learning**: CNN-based classification for three categories:
  - Normal feet
  - Hallux valgus (bunions)
  - Flatfoot
- **Backend API**: FastAPI application serving the ML model
- **Frontend**: React-based web interface for image upload and prediction
- **Real-time Predictions**: Instant classification with confidence scores
- **Medical-grade Interface**: Professional design suitable for medical use

## Technology Stack

- **Machine Learning**: Python, TensorFlow/Keras, OpenCV, NumPy, Pandas
- **Backend**: FastAPI, Python, Uvicorn
- **Frontend**: React, Vite, TypeScript, Tailwind CSS
- **Data Processing**: PIL, scikit-learn, matplotlib, seaborn

## Quick Start

### Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn

### Installation

1. Clone the repository
2. Set up the ML environment:
   ```bash
   cd ml_model
   pip install -r requirements.txt
   ```

3. Set up the API:
   ```bash
   cd api
   pip install -r requirements.txt
   ```

4. Set up the frontend:
   ```bash
   cd frontend
   npm install
   ```

### Usage

1. Train the model (if needed):
   ```bash
   cd ml_model
   python train_model.py
   ```

2. Start the API server:
   ```bash
   cd api
   uvicorn app.main:app --reload
   ```

3. Start the frontend:
   ```bash
   cd frontend
   npm run dev
   ```

4. Open your browser to `http://localhost:3000`

## Model Performance

The CNN model achieves:
- Overall accuracy: [To be updated after training]
- Precision/Recall/F1-score for each class
- Comprehensive evaluation metrics and visualizations

## API Endpoints

- `POST /predict`: Upload image and get prediction
- `GET /health`: Health check endpoint
- `GET /model-info`: Model information and metadata

## Contributing

Please read the documentation in the `docs/` folder for development guidelines.

## License

[To be determined]
