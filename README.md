# Foot Deformity Detection System

A comprehensive web application for detecting foot deformities using deep learning, specifically focusing on hallux valgus (bunions) and flatfoot conditions.

## Project Structure

```
foot-deformity-detection/
├── Dataset/                    # Original dataset files
│   ├── Flatfoot/              # Flatfoot detection datasets
│   └── Hallux Valgus/         # Hallux valgus datasets
├── ml_model/                  # Machine Learning components
│   ├── data_preprocessing/    # Data preparation scripts
│   ├── models/               # CNN model definitions
│   ├── training/             # Training scripts and utilities
│   ├── evaluation/           # Model evaluation and metrics
│   └── saved_models/         # Trained model files
├── api/                      # FastAPI backend
│   ├── app/                  # Main application code
│   ├── models/               # API data models
│   ├── routes/               # API endpoints
│   └── utils/                # Utility functions
├── frontend/                 # React frontend
│   ├── src/                  # Source code
│   ├── public/               # Static assets
│   └── components/           # React components
└── docs/                     # Documentation
```

## Features

- **Machine Learning**: CNN-based classification for three categories:
  - Normal feet
  - Hallux valgus (bunions)
  - Flatfoot
- **Backend API**: FastAPI application serving the ML model
- **Frontend**: React-based web interface for image upload and prediction
- **Real-time Predictions**: Instant classification with confidence scores
- **Medical-grade Interface**: Professional design suitable for medical use

## Technology Stack

- **Machine Learning**: Python, TensorFlow/Keras, OpenCV, NumPy, Pandas
- **Backend**: FastAPI, Python, Uvicorn
- **Frontend**: React, Vite, TypeScript, Tailwind CSS
- **Data Processing**: PIL, scikit-learn, matplotlib, seaborn

## Quick Start

### Prerequisites

- Python 3.8+
- 8GB+ RAM (16GB recommended for training)
- 5GB+ storage space

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd foot-deformity-detection
   ```

2. **Set up the ML environment:**
   ```bash
   cd ml_model
   pip3 install -r requirements.txt
   ```

3. **Set up the API:**
   ```bash
   cd ../api
   pip3 install -r requirements.txt
   ```

### Usage

1. **Start the API server:**
   ```bash
   cd api
   python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Open the web interface:**
   - Open `frontend/demo.html` in your web browser
   - Or navigate to: `file:///path/to/project/frontend/demo.html`

3. **Test the system:**
   - Upload a foot image using the web interface
   - View real-time predictions with confidence scores
   - Get medical recommendations based on the analysis

### API Testing
```bash
# Health check
curl http://localhost:8000/health

# Test prediction
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@path/to/foot/image.jpg"
```

## Model Performance

The CNN model achieves:
- **Overall Training Accuracy**: 74.18%
- **Model Parameters**: 1.44M parameters
- **Training Time**: ~30 epochs (1-2 hours on CPU)
- **Architecture**: Custom CNN with batch normalization and dropout
- **Input Size**: 224x224x3 RGB images
- **Classes**: Normal, Flatfoot, Hallux Valgus

### Training Metrics
- **Precision**: 75.40%
- **Recall**: 73.43%
- **Loss**: Converged to ~0.77
- **Validation**: Model saved with best validation accuracy

## API Endpoints

- `POST /predict`: Upload image and get prediction
- `GET /health`: Health check endpoint
- `GET /model-info`: Model information and metadata

## Contributing

Please read the documentation in the `docs/` folder for development guidelines.

## License

[To be determined]
