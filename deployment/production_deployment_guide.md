# 🚀 Production Deployment Guide

## Overview
This guide covers deploying the Foot Deformity Detection System to production environments including cloud platforms, containerization, and scaling strategies.

## 📋 Prerequisites

### System Requirements
- **CPU**: 4+ cores recommended
- **RAM**: 8GB+ (16GB for high traffic)
- **Storage**: 10GB+ free space
- **GPU**: Optional (NVIDIA GPU for faster inference)
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows

### Software Dependencies
- Python 3.8+
- Node.js 16+
- Docker (optional but recommended)
- NGINX (for production web server)

## 🐳 Docker Deployment

### 1. Create Dockerfile for API

```dockerfile
# api/Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. Create Dockerfile for Frontend

```dockerfile
# frontend/Dockerfile
FROM node:16-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 3. Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: ./api
    ports:
      - "8000:8000"
    volumes:
      - ./ml_model:/app/ml_model
    environment:
      - MODEL_PATH=/app/ml_model/saved_models/best_model.keras
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - api
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    restart: unless-stopped
```

## ☁️ Cloud Platform Deployment

### AWS Deployment

#### 1. EC2 Instance Setup
```bash
# Launch EC2 instance (t3.large recommended)
# Install Docker and Docker Compose
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. ECS Deployment (Recommended)
```json
{
  "family": "foot-deformity-api",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "api",
      "image": "your-account.dkr.ecr.region.amazonaws.com/foot-deformity-api:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/foot-deformity-api",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Platform (GCP)

#### 1. Cloud Run Deployment
```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/foot-deformity-api ./api

# Deploy to Cloud Run
gcloud run deploy foot-deformity-api \
  --image gcr.io/PROJECT-ID/foot-deformity-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2
```

#### 2. GKE Deployment
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: foot-deformity-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: foot-deformity-api
  template:
    metadata:
      labels:
        app: foot-deformity-api
    spec:
      containers:
      - name: api
        image: gcr.io/PROJECT-ID/foot-deformity-api:latest
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: foot-deformity-service
spec:
  selector:
    app: foot-deformity-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

### Azure Deployment

#### Container Instances
```bash
# Create resource group
az group create --name foot-deformity-rg --location eastus

# Deploy container
az container create \
  --resource-group foot-deformity-rg \
  --name foot-deformity-api \
  --image your-registry/foot-deformity-api:latest \
  --cpu 2 \
  --memory 4 \
  --ports 8000 \
  --dns-name-label foot-deformity-unique
```

## 🔧 Production Configuration

### Environment Variables
```bash
# .env.production
MODEL_PATH=/app/models/best_model.keras
LOG_LEVEL=INFO
MAX_FILE_SIZE=10485760  # 10MB
CORS_ORIGINS=https://yourdomain.com
DATABASE_URL=********************************/db
REDIS_URL=redis://redis:6379/0
```

### NGINX Configuration
```nginx
# nginx/nginx.conf
upstream api {
    server api:8000;
}

server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    client_max_body_size 10M;

    location /api/ {
        proxy_pass http://api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
}
```

## 📊 Monitoring and Logging

### Health Checks
```python
# Enhanced health check endpoint
@app.get("/health/detailed")
async def detailed_health():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "model_loaded": model is not None,
        "memory_usage": psutil.virtual_memory().percent,
        "cpu_usage": psutil.cpu_percent(),
        "disk_usage": psutil.disk_usage('/').percent,
        "uptime": time.time() - start_time
    }
```

### Logging Configuration
```python
# logging_config.py
import logging
from pythonjsonlogger import jsonlogger

def setup_logging():
    logHandler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter()
    logHandler.setFormatter(formatter)
    logger = logging.getLogger()
    logger.addHandler(logHandler)
    logger.setLevel(logging.INFO)
```

## 🔒 Security Considerations

### API Security
- Rate limiting
- Input validation
- HTTPS only
- API key authentication
- CORS configuration

### Infrastructure Security
- VPC/Network security groups
- SSL/TLS certificates
- Regular security updates
- Backup strategies

## 📈 Scaling Strategies

### Horizontal Scaling
- Load balancers
- Multiple API instances
- Database read replicas
- CDN for static assets

### Performance Optimization
- Model quantization
- Caching strategies
- Async processing
- GPU acceleration

## 🚀 Deployment Commands

### Quick Start
```bash
# Clone and deploy
git clone <repository>
cd foot-deformity-classifier
docker-compose up -d

# Check status
docker-compose ps
curl http://localhost:8000/health
```

### Production Deployment
```bash
# Build for production
docker-compose -f docker-compose.prod.yml build

# Deploy with SSL
docker-compose -f docker-compose.prod.yml up -d

# Monitor logs
docker-compose logs -f api
```

This guide provides comprehensive deployment options from development to enterprise-scale production environments.
