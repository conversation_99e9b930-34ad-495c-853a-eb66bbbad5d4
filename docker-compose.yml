version: '3.8'

services:
  # Main API service
  api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./ml_model:/app/ml_model
      - ./logs:/app/logs
      - ./evaluation_results:/app/evaluation_results
    environment:
      - MODEL_PATH=/app/ml_model/saved_models/best_model.keras
      - LOG_LEVEL=INFO
      - CORS_ORIGINS=http://localhost:3000,http://localhost:80
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - foot-deformity-network

  # Frontend service (if using a separate frontend server)
  frontend:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - foot-deformity-network

  # Analytics dashboard
  dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    ports:
      - "8501:8501"
    volumes:
      - ./monitoring:/app/monitoring
      - ./analytics.db:/app/analytics.db
    environment:
      - API_URL=http://api:8000
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - foot-deformity-network

  # Redis for caching (optional)
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - foot-deformity-network

  # PostgreSQL for production database (optional)
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: foot_deformity
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - foot-deformity-network

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - frontend
      - dashboard
    restart: unless-stopped
    networks:
      - foot-deformity-network

volumes:
  redis_data:
  postgres_data:

networks:
  foot-deformity-network:
    driver: bridge
