#!/usr/bin/env python3
"""
Analytics Dashboard for Foot Deformity Detection System
Real-time monitoring, performance metrics, and usage analytics
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import json
import time
from datetime import datetime, timedelta
import sqlite3
import psutil
import os

class AnalyticsDashboard:
    def __init__(self, api_url="http://localhost:8000", db_path="analytics.db"):
        self.api_url = api_url
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize analytics database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS predictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                predicted_class TEXT,
                confidence REAL,
                processing_time REAL,
                file_size INTEGER,
                user_ip TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                cpu_usage REAL,
                memory_usage REAL,
                disk_usage REAL,
                api_response_time REAL,
                active_connections INTEGER
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_api_health(self):
        """Get API health status"""
        try:
            response = requests.get(f"{self.api_url}/health", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "unhealthy", "error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"status": "unreachable", "error": str(e)}
    
    def get_model_info(self):
        """Get model information"""
        try:
            response = requests.get(f"{self.api_url}/model-info", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except:
            return None
    
    def get_system_metrics(self):
        """Get current system metrics"""
        return {
            "cpu_usage": psutil.cpu_percent(interval=1),
            "memory_usage": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "timestamp": datetime.now()
        }
    
    def log_prediction(self, predicted_class, confidence, processing_time, file_size, user_ip="unknown"):
        """Log prediction to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO predictions (predicted_class, confidence, processing_time, file_size, user_ip)
            VALUES (?, ?, ?, ?, ?)
        ''', (predicted_class, confidence, processing_time, file_size, user_ip))
        
        conn.commit()
        conn.close()
    
    def log_system_metrics(self, metrics):
        """Log system metrics to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO system_metrics (cpu_usage, memory_usage, disk_usage, api_response_time)
            VALUES (?, ?, ?, ?)
        ''', (metrics['cpu_usage'], metrics['memory_usage'], metrics['disk_usage'], 
              metrics.get('api_response_time', 0)))
        
        conn.commit()
        conn.close()
    
    def get_prediction_stats(self, days=7):
        """Get prediction statistics"""
        conn = sqlite3.connect(self.db_path)
        
        # Get predictions from last N days
        query = '''
            SELECT * FROM predictions 
            WHERE timestamp >= datetime('now', '-{} days')
            ORDER BY timestamp DESC
        '''.format(days)
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        return df
    
    def get_system_stats(self, hours=24):
        """Get system statistics"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT * FROM system_metrics 
            WHERE timestamp >= datetime('now', '-{} hours')
            ORDER BY timestamp DESC
        '''.format(hours)
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        return df

def main():
    """Main dashboard function"""
    st.set_page_config(
        page_title="Foot Deformity Detection Analytics",
        page_icon="🦶",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize dashboard
    dashboard = AnalyticsDashboard()
    
    # Sidebar
    st.sidebar.title("🦶 Analytics Dashboard")
    st.sidebar.markdown("---")
    
    # Refresh button
    if st.sidebar.button("🔄 Refresh Data"):
        st.experimental_rerun()
    
    # Time range selector
    time_range = st.sidebar.selectbox(
        "📅 Time Range",
        ["Last Hour", "Last 24 Hours", "Last 7 Days", "Last 30 Days"]
    )
    
    # Auto-refresh toggle
    auto_refresh = st.sidebar.checkbox("🔄 Auto Refresh (30s)")
    
    if auto_refresh:
        time.sleep(30)
        st.experimental_rerun()
    
    # Main dashboard
    st.title("🦶 Foot Deformity Detection System Analytics")
    st.markdown("Real-time monitoring and performance analytics")
    
    # System Status
    st.header("🏥 System Status")
    
    col1, col2, col3, col4 = st.columns(4)
    
    # API Health
    health = dashboard.get_api_health()
    with col1:
        if health["status"] == "healthy":
            st.metric("API Status", "🟢 Healthy", "Online")
        else:
            st.metric("API Status", "🔴 Unhealthy", health.get("error", "Unknown"))
    
    # Model Info
    model_info = dashboard.get_model_info()
    with col2:
        if model_info:
            st.metric("Model", "🧠 Loaded", f"{model_info.get('total_parameters', 0):,} params")
        else:
            st.metric("Model", "❌ Not Loaded", "Error")
    
    # System Metrics
    sys_metrics = dashboard.get_system_metrics()
    with col3:
        st.metric("CPU Usage", f"{sys_metrics['cpu_usage']:.1f}%", 
                 "🟢 Normal" if sys_metrics['cpu_usage'] < 80 else "🟡 High")
    
    with col4:
        st.metric("Memory Usage", f"{sys_metrics['memory_usage']:.1f}%",
                 "🟢 Normal" if sys_metrics['memory_usage'] < 80 else "🟡 High")
    
    # Prediction Analytics
    st.header("📊 Prediction Analytics")
    
    # Get prediction data
    pred_df = dashboard.get_prediction_stats(7)
    
    if not pred_df.empty:
        col1, col2 = st.columns(2)
        
        with col1:
            # Prediction distribution
            class_counts = pred_df['predicted_class'].value_counts()
            fig_pie = px.pie(
                values=class_counts.values,
                names=class_counts.index,
                title="Prediction Distribution (Last 7 Days)"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            # Confidence distribution
            fig_hist = px.histogram(
                pred_df,
                x='confidence',
                nbins=20,
                title="Confidence Score Distribution"
            )
            st.plotly_chart(fig_hist, use_container_width=True)
        
        # Predictions over time
        pred_df['timestamp'] = pd.to_datetime(pred_df['timestamp'])
        pred_df['hour'] = pred_df['timestamp'].dt.floor('H')
        hourly_counts = pred_df.groupby('hour').size().reset_index(name='count')
        
        fig_time = px.line(
            hourly_counts,
            x='hour',
            y='count',
            title="Predictions Over Time (Hourly)"
        )
        st.plotly_chart(fig_time, use_container_width=True)
        
        # Performance metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            avg_confidence = pred_df['confidence'].mean()
            st.metric("Average Confidence", f"{avg_confidence:.3f}")
        
        with col2:
            avg_processing_time = pred_df['processing_time'].mean()
            st.metric("Avg Processing Time", f"{avg_processing_time:.3f}s")
        
        with col3:
            total_predictions = len(pred_df)
            st.metric("Total Predictions", total_predictions)
    
    else:
        st.info("No prediction data available. Start making predictions to see analytics.")
    
    # System Performance
    st.header("⚡ System Performance")
    
    sys_df = dashboard.get_system_stats(24)
    
    if not sys_df.empty:
        sys_df['timestamp'] = pd.to_datetime(sys_df['timestamp'])
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('CPU Usage', 'Memory Usage', 'Disk Usage', 'API Response Time'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # CPU Usage
        fig.add_trace(
            go.Scatter(x=sys_df['timestamp'], y=sys_df['cpu_usage'], name='CPU %'),
            row=1, col=1
        )
        
        # Memory Usage
        fig.add_trace(
            go.Scatter(x=sys_df['timestamp'], y=sys_df['memory_usage'], name='Memory %'),
            row=1, col=2
        )
        
        # Disk Usage
        fig.add_trace(
            go.Scatter(x=sys_df['timestamp'], y=sys_df['disk_usage'], name='Disk %'),
            row=2, col=1
        )
        
        # API Response Time
        if 'api_response_time' in sys_df.columns:
            fig.add_trace(
                go.Scatter(x=sys_df['timestamp'], y=sys_df['api_response_time'], name='Response Time (s)'),
                row=2, col=2
            )
        
        fig.update_layout(height=600, showlegend=False, title_text="System Metrics (Last 24 Hours)")
        st.plotly_chart(fig, use_container_width=True)
    
    # Recent Predictions Table
    st.header("📋 Recent Predictions")
    
    if not pred_df.empty:
        recent_predictions = pred_df.head(10)[['timestamp', 'predicted_class', 'confidence', 'processing_time']]
        recent_predictions['confidence'] = recent_predictions['confidence'].round(4)
        recent_predictions['processing_time'] = recent_predictions['processing_time'].round(3)
        st.dataframe(recent_predictions, use_container_width=True)
    else:
        st.info("No recent predictions to display.")
    
    # Model Performance Summary
    st.header("🎯 Model Performance Summary")
    
    if model_info:
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Model Details")
            st.write(f"**Classes**: {', '.join(model_info.get('class_names', []))}")
            st.write(f"**Input Shape**: {model_info.get('input_shape', 'Unknown')}")
            st.write(f"**Parameters**: {model_info.get('total_parameters', 0):,}")
            st.write(f"**Model Loaded**: {'✅ Yes' if model_info.get('model_loaded') else '❌ No'}")
        
        with col2:
            if not pred_df.empty:
                st.subheader("Performance Metrics")
                
                # Calculate accuracy by class (if ground truth available)
                class_performance = pred_df.groupby('predicted_class').agg({
                    'confidence': ['mean', 'std', 'count']
                }).round(3)
                
                st.dataframe(class_performance)
    
    # Footer
    st.markdown("---")
    st.markdown("🦶 Foot Deformity Detection System | Analytics Dashboard v1.0")

if __name__ == "__main__":
    main()
