#!/usr/bin/env python3
"""
Test Bias Correction - Quick test to verify the bias correction is working
"""

import requests
import json
import time

def test_api_with_bias_correction():
    """Test the API with bias correction"""
    
    api_url = "http://localhost:8000"
    
    print("🧪 TESTING BIAS CORRECTION")
    print("="*50)
    
    # Test 1: Check API health
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API is running")
        else:
            print("❌ API health check failed")
            return False
    except Exception as e:
        print(f"❌ API not accessible: {e}")
        print("💡 Please start the API server:")
        print("   cd api && python3 -m uvicorn app.main:app --host 0.0.0.0 --port 8000")
        return False
    
    # Test 2: Test predictions with different images
    test_images = [
        "ml_model/processed_dataset/test/flatfoot/flatfoot_Flatfoot14_jpg.rf.cb566d841627a53a540d5a5fee993c50.jpg",
        "ml_model/processed_dataset/test/hallux_valgus/hv_HV3-48-ARR-_jpg.rf.6c3b93ec4966aca032bc23bd7c08361f.jpg",
        "ml_model/processed_dataset/test/normal/flatfoot_Flatfoot13_jpg.rf.4148db8039e51d129a38c2eaa88db058.jpg"
    ]
    
    expected_classes = ["flatfoot", "hallux_valgus", "normal"]
    
    print(f"\n🔍 Testing predictions with bias correction:")
    print("-" * 50)
    
    correct_predictions = 0
    total_predictions = 0
    
    for i, (image_path, expected_class) in enumerate(zip(test_images, expected_classes)):
        print(f"\nTest {i+1}: {expected_class} image")
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(f"{api_url}/predict", files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                predicted_class = result['prediction']['class']
                confidence = result['prediction']['confidence']
                
                print(f"  Expected: {expected_class}")
                print(f"  Predicted: {predicted_class}")
                print(f"  Confidence: {confidence:.3f}")
                
                # Show bias correction details
                if 'debug' in result:
                    raw_probs = result['debug']['raw_probabilities']
                    corrected_probs = result['probabilities']
                    
                    print(f"  Raw probabilities:")
                    for class_name, prob in raw_probs.items():
                        print(f"    {class_name}: {prob:.3f}")
                    
                    print(f"  Corrected probabilities:")
                    for class_name, prob in corrected_probs.items():
                        print(f"    {class_name}: {prob:.3f}")
                    
                    # Check if flatfoot got a boost
                    raw_flatfoot = raw_probs['flatfoot']
                    corrected_flatfoot = corrected_probs['flatfoot']
                    
                    if corrected_flatfoot > raw_flatfoot:
                        print(f"  ✅ Flatfoot boosted: {raw_flatfoot:.3f} → {corrected_flatfoot:.3f}")
                    else:
                        print(f"  ⚠️ Flatfoot not boosted: {raw_flatfoot:.3f} → {corrected_flatfoot:.3f}")
                
                # Check if prediction is correct
                if predicted_class == expected_class:
                    print(f"  ✅ CORRECT PREDICTION")
                    correct_predictions += 1
                else:
                    print(f"  ❌ INCORRECT PREDICTION")
                
                total_predictions += 1
                
            else:
                print(f"  ❌ API error: {response.status_code}")
                print(f"  Response: {response.text}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")
    
    # Summary
    print(f"\n📊 BIAS CORRECTION TEST RESULTS:")
    print("-" * 40)
    
    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        print(f"Accuracy: {accuracy:.1%} ({correct_predictions}/{total_predictions})")
        
        if accuracy >= 0.67:  # At least 2/3 correct
            print("✅ Bias correction appears to be working!")
        else:
            print("⚠️ Bias correction may need adjustment")
    else:
        print("❌ No successful predictions made")
    
    return total_predictions > 0

def quick_bias_test():
    """Quick test of bias correction logic"""
    
    print(f"\n🔧 QUICK BIAS CORRECTION TEST:")
    print("-" * 35)
    
    import numpy as np
    
    # Simulate typical biased predictions (favoring hallux_valgus)
    test_cases = [
        [0.1, 0.8, 0.1],  # Heavily biased towards hallux_valgus
        [0.05, 0.9, 0.05],  # Very heavily biased
        [0.2, 0.6, 0.2],   # Moderately biased
        [0.33, 0.34, 0.33]  # Relatively balanced
    ]
    
    class_names = ['flatfoot', 'hallux_valgus', 'normal']
    bias_factors = np.array([1.8, 0.6, 1.3])  # [flatfoot, hallux_valgus, normal]
    
    for i, raw_probs in enumerate(test_cases):
        print(f"\nTest case {i+1}:")
        
        # Apply bias correction
        corrected_probs = np.array(raw_probs) * bias_factors
        corrected_probs = corrected_probs / np.sum(corrected_probs)
        
        print(f"  Raw:       {dict(zip(class_names, raw_probs))}")
        print(f"  Corrected: {dict(zip(class_names, corrected_probs))}")
        
        raw_winner = class_names[np.argmax(raw_probs)]
        corrected_winner = class_names[np.argmax(corrected_probs)]
        
        print(f"  Raw winner: {raw_winner}")
        print(f"  Corrected winner: {corrected_winner}")
        
        if raw_winner != corrected_winner:
            print(f"  🔄 Prediction changed!")
        else:
            print(f"  ➡️ Prediction unchanged")

def main():
    """Main test function"""
    
    print("🚀 BIAS CORRECTION TESTING")
    print("="*60)
    
    # Quick logic test
    quick_bias_test()
    
    # API test
    print(f"\n" + "="*60)
    api_working = test_api_with_bias_correction()
    
    if api_working:
        print(f"\n🎉 BIAS CORRECTION TESTING COMPLETE!")
        print("✅ The bias correction is now active and should help with flatfoot detection")
        print("\n💡 Next steps:")
        print("1. Test with the professional frontend")
        print("2. Upload different foot X-ray images")
        print("3. Verify that flatfoot images are now detected correctly")
        print("4. Wait for the balanced model training to complete for even better results")
    else:
        print(f"\n❌ API testing failed. Please start the API server and try again.")

if __name__ == "__main__":
    main()
