#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the model loading issue by waiting for better training results
and then restarting the API with the improved model.
"""

import time
import os
import requests
import subprocess
import signal

def check_training_progress():
    """Check if training has progressed beyond the problematic early epochs."""
    # Look for training log or model files with better performance
    model_path = "ml_model/saved_models/best_model.keras"
    if os.path.exists(model_path):
        # Get file modification time
        mod_time = os.path.getmtime(model_path)
        current_time = time.time()
        
        # If model was modified recently (within last 10 minutes), it might be better
        if current_time - mod_time < 600:  # 10 minutes
            return True
    return False

def test_api_prediction():
    """Test if the API is giving diverse predictions (not just 'normal')."""
    try:
        # Test with a known flatfoot image
        test_image = "ml_model/processed_dataset/test/flatfoot/flatfoot_Flatfoot14_jpg.rf.cb566d841627a53a540d5a5fee993c50.jpg"
        
        if not os.path.exists(test_image):
            print(f"Test image not found: {test_image}")
            return False
            
        with open(test_image, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8000/predict', files=files, timeout=30)
            
        if response.status_code == 200:
            result = response.json()
            prediction = result.get('prediction', {})
            probabilities = result.get('probabilities', {})
            
            print(f"Current prediction: {prediction.get('class')} ({prediction.get('confidence', 0):.3f})")
            print(f"Probabilities: {probabilities}")
            
            # Check if model is showing more diverse predictions
            normal_prob = probabilities.get('normal', 1.0)
            if normal_prob < 0.9:  # If normal probability is less than 90%, model is improving
                return True
                
        return False
        
    except Exception as e:
        print(f"Error testing API: {e}")
        return False

def restart_api_server():
    """Restart the API server to load the updated model."""
    print("Attempting to restart API server...")
    
    # Kill existing API processes
    try:
        subprocess.run(['pkill', '-f', 'uvicorn.*app.main:app'], check=False)
        time.sleep(2)
    except:
        pass
    
    # Start new API server
    try:
        process = subprocess.Popen([
            'python3', '-m', 'uvicorn', 'app.main:app', 
            '--reload', '--host', '0.0.0.0', '--port', '8000'
        ], cwd='api')
        
        # Wait a bit for server to start
        time.sleep(10)
        
        # Test if server is responding
        response = requests.get('http://localhost:8000/health', timeout=10)
        if response.status_code == 200:
            print("API server restarted successfully!")
            return True
        else:
            print("API server restart failed - health check failed")
            return False
            
    except Exception as e:
        print(f"Error restarting API server: {e}")
        return False

def main():
    """Main function to monitor training and fix the model loading issue."""
    print("Monitoring training progress and API performance...")
    
    max_attempts = 30  # Check for 30 iterations (about 15 minutes)
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"\nAttempt {attempt}/{max_attempts}")
        
        # Check if training has progressed
        if check_training_progress():
            print("Training appears to have progressed - testing API...")
            
            # Test current API performance
            if not test_api_prediction():
                print("API still showing poor predictions - restarting server...")
                if restart_api_server():
                    # Test again after restart
                    time.sleep(5)
                    if test_api_prediction():
                        print("✅ API is now working better!")
                        break
                    else:
                        print("API still not improved after restart")
                else:
                    print("Failed to restart API server")
            else:
                print("✅ API is already working well!")
                break
        else:
            print("Training still in progress...")
        
        # Wait before next check
        time.sleep(30)  # Wait 30 seconds
    
    if attempt >= max_attempts:
        print("⚠️  Maximum attempts reached. Manual intervention may be needed.")
        print("The training is progressing well, but the API may need manual restart.")

if __name__ == "__main__":
    main()
