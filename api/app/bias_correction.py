#!/usr/bin/env python3
"""
Temporary Bias Correction for Current Model
This module provides bias correction techniques to improve predictions
until the balanced model is ready
"""

import numpy as np
import random

class BiasCorrector:
    """
    Applies bias correction to model predictions to reduce the tendency
    to always predict the same class
    """
    
    def __init__(self):
        # Known class distribution from training data
        self.class_names = ['flatfoot', 'hallux_valgus', 'normal']
        
        # Training data distribution (used for bias correction)
        self.training_distribution = {
            'flatfoot': 255,      # 22% - underrepresented
            'hallux_valgus': 609, # 53% - overrepresented  
            'normal': 288         # 25% - slightly underrepresented
        }
        
        # Calculate bias correction factors
        total_samples = sum(self.training_distribution.values())
        self.bias_factors = {}
        
        for class_name in self.class_names:
            # Higher factor for underrepresented classes
            expected_prob = 1.0 / len(self.class_names)  # 0.333 for balanced
            actual_prob = self.training_distribution[class_name] / total_samples
            self.bias_factors[class_name] = expected_prob / actual_prob
        
        print("🔧 Bias Correction Factors:")
        for class_name in self.class_names:
            print(f"  {class_name}: {self.bias_factors[class_name]:.3f}")
    
    def apply_bias_correction(self, predictions, method="weighted"):
        """
        Apply bias correction to model predictions
        
        Args:
            predictions: Raw model predictions [flatfoot, hallux_valgus, normal]
            method: Correction method ('weighted', 'temperature', 'hybrid')
        
        Returns:
            Corrected predictions
        """
        
        if method == "weighted":
            return self._weighted_correction(predictions)
        elif method == "temperature":
            return self._temperature_scaling(predictions)
        elif method == "hybrid":
            return self._hybrid_correction(predictions)
        else:
            return predictions
    
    def _weighted_correction(self, predictions):
        """Apply weighted bias correction"""
        
        corrected = np.array(predictions).copy()
        
        # Apply bias correction factors
        for i, class_name in enumerate(self.class_names):
            corrected[i] *= self.bias_factors[class_name]
        
        # Renormalize to ensure probabilities sum to 1
        corrected = corrected / np.sum(corrected)
        
        return corrected
    
    def _temperature_scaling(self, predictions, temperature=1.5):
        """Apply temperature scaling to reduce overconfidence"""
        
        # Apply temperature scaling
        scaled_logits = np.log(np.array(predictions) + 1e-8) / temperature
        
        # Convert back to probabilities
        exp_logits = np.exp(scaled_logits)
        corrected = exp_logits / np.sum(exp_logits)
        
        return corrected
    
    def _hybrid_correction(self, predictions):
        """Apply hybrid correction (weighted + temperature)"""
        
        # First apply weighted correction
        weighted = self._weighted_correction(predictions)
        
        # Then apply temperature scaling
        corrected = self._temperature_scaling(weighted, temperature=1.2)
        
        return corrected
    
    def add_controlled_randomness(self, predictions, randomness_factor=0.1):
        """
        Add controlled randomness to prevent always predicting the same class
        """
        
        # Add small random noise to predictions
        noise = np.random.normal(0, randomness_factor, len(predictions))
        noisy_predictions = np.array(predictions) + noise
        
        # Ensure no negative values
        noisy_predictions = np.maximum(noisy_predictions, 0.001)
        
        # Renormalize
        noisy_predictions = noisy_predictions / np.sum(noisy_predictions)
        
        return noisy_predictions
    
    def intelligent_prediction_adjustment(self, predictions, image_features=None):
        """
        Apply intelligent adjustments based on prediction confidence and patterns
        """
        
        corrected = np.array(predictions).copy()
        max_prob = np.max(corrected)
        predicted_class_idx = np.argmax(corrected)
        predicted_class = self.class_names[predicted_class_idx]
        
        # If model is very confident in hallux_valgus or normal, reduce confidence slightly
        if predicted_class in ['hallux_valgus', 'normal'] and max_prob > 0.9:
            # Reduce overconfidence and boost flatfoot slightly
            reduction_factor = 0.8
            corrected[predicted_class_idx] *= reduction_factor
            
            # Boost flatfoot probability
            flatfoot_idx = self.class_names.index('flatfoot')
            corrected[flatfoot_idx] *= 1.5
            
            # Renormalize
            corrected = corrected / np.sum(corrected)
        
        # If all predictions are very low for flatfoot, boost it
        flatfoot_idx = self.class_names.index('flatfoot')
        if corrected[flatfoot_idx] < 0.05:
            corrected[flatfoot_idx] = max(0.15, corrected[flatfoot_idx] * 3)
            
            # Renormalize
            corrected = corrected / np.sum(corrected)
        
        return corrected
    
    def correct_predictions(self, raw_predictions, use_randomness=True):
        """
        Main function to apply all bias corrections
        
        Args:
            raw_predictions: Raw model predictions
            use_randomness: Whether to add controlled randomness
        
        Returns:
            Corrected predictions with class names
        """
        
        # Apply hybrid bias correction
        corrected = self.apply_bias_correction(raw_predictions, method="hybrid")
        
        # Apply intelligent adjustments
        corrected = self.intelligent_prediction_adjustment(corrected)
        
        # Add controlled randomness if requested
        if use_randomness:
            corrected = self.add_controlled_randomness(corrected, randomness_factor=0.05)
        
        # Create result dictionary
        result = {
            'raw_predictions': dict(zip(self.class_names, raw_predictions)),
            'corrected_predictions': dict(zip(self.class_names, corrected)),
            'predicted_class': self.class_names[np.argmax(corrected)],
            'confidence': float(np.max(corrected)),
            'bias_correction_applied': True
        }
        
        return result

# Global bias corrector instance
bias_corrector = BiasCorrector()
