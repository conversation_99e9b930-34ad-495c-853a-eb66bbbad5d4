"""
FastAPI Main Application for Foot Deformity Detection

This is the main FastAPI application that serves the trained CNN model
for foot deformity classification.
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
import sys
import numpy as np
from PIL import Image
import io
import tensorflow as tf
from typing import Dict, List
import json
from datetime import datetime

# Add the ml_model directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'ml_model'))

from models.cnn_model import FootDeformityCNN

# Initialize FastAPI app
app = FastAPI(
    title="Foot Deformity Detection API",
    description="API for detecting foot deformities using deep learning",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model
model = None
class_names = ['flatfoot', 'hallux_valgus', 'normal']

def load_model():
    """Load the trained model."""
    global model
    try:
        # Try different possible paths
        possible_paths = [
            "ml_model/saved_models/best_model.keras",
            "../ml_model/saved_models/best_model.keras",
            "ml_model/saved_models/final_model.keras",
            "../ml_model/saved_models/final_model.keras"
        ]

        for model_path in possible_paths:
            if os.path.exists(model_path):
                model = tf.keras.models.load_model(model_path)
                print(f"Model loaded successfully from {model_path}")
                return

        print("No trained model found. Please train the model first.")
        print("Checked paths:", possible_paths)
        model = None
    except Exception as e:
        print(f"Error loading model: {e}")
        model = None

def preprocess_image(image: Image.Image) -> np.ndarray:
    """Preprocess the uploaded image for model prediction."""
    try:
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize to model input size
        image = image.resize((224, 224))
        
        # Convert to numpy array and normalize
        image_array = np.array(image) / 255.0
        
        # Add batch dimension
        image_array = np.expand_dims(image_array, axis=0)
        
        return image_array
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error preprocessing image: {str(e)}")

@app.on_event("startup")
async def startup_event():
    """Load the model when the application starts."""
    load_model()

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Foot Deformity Detection API",
        "version": "1.0.0",
        "status": "running",
        "model_loaded": model is not None,
        "endpoints": {
            "predict": "/predict",
            "health": "/health",
            "model-info": "/model-info"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "model_loaded": model is not None
    }

@app.get("/model-info")
async def model_info():
    """Get information about the loaded model."""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    try:
        # Get model summary information
        total_params = model.count_params()
        input_shape = model.input_shape
        output_shape = model.output_shape
        
        return {
            "model_loaded": True,
            "input_shape": input_shape,
            "output_shape": output_shape,
            "total_parameters": int(total_params),
            "class_names": class_names,
            "num_classes": len(class_names)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting model info: {str(e)}")

@app.post("/predict")
async def predict_deformity(file: UploadFile = File(...)):
    """
    Predict foot deformity from uploaded image.
    
    Args:
        file: Uploaded image file (JPEG, PNG)
    
    Returns:
        JSON response with prediction results and confidence scores
    """
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded. Please check server logs.")
    
    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        # Read and process the image
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # Preprocess the image
        processed_image = preprocess_image(image)
        
        # Make prediction
        predictions = model.predict(processed_image, verbose=0)
        prediction_probs = predictions[0]
        
        # Get the predicted class
        predicted_class_idx = np.argmax(prediction_probs)
        predicted_class = class_names[predicted_class_idx]
        confidence = float(prediction_probs[predicted_class_idx])
        
        # Create detailed results
        class_probabilities = {
            class_names[i]: float(prediction_probs[i]) 
            for i in range(len(class_names))
        }
        
        # Determine severity/recommendation based on prediction
        if predicted_class == 'normal':
            severity = "Normal"
            recommendation = "No deformity detected. Maintain good foot health."
        elif predicted_class == 'flatfoot':
            severity = "Mild to Moderate"
            recommendation = "Flatfoot detected. Consider consulting a podiatrist for proper assessment and potential treatment options."
        else:  # hallux_valgus
            severity = "Mild to Severe"
            recommendation = "Hallux valgus (bunion) detected. Recommend consultation with a foot specialist for evaluation and treatment planning."
        
        result = {
            "success": True,
            "prediction": {
                "class": predicted_class,
                "confidence": confidence,
                "severity": severity,
                "recommendation": recommendation
            },
            "probabilities": class_probabilities,
            "metadata": {
                "filename": file.filename,
                "image_size": f"{image.size[0]}x{image.size[1]}",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        return JSONResponse(content=result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")

@app.post("/batch-predict")
async def batch_predict(files: List[UploadFile] = File(...)):
    """
    Predict foot deformities for multiple images.
    
    Args:
        files: List of uploaded image files
    
    Returns:
        JSON response with prediction results for each image
    """
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    if len(files) > 10:  # Limit batch size
        raise HTTPException(status_code=400, detail="Maximum 10 files allowed per batch")
    
    results = []
    
    for file in files:
        try:
            # Validate file type
            if not file.content_type.startswith('image/'):
                results.append({
                    "filename": file.filename,
                    "success": False,
                    "error": "File must be an image"
                })
                continue
            
            # Process image
            image_data = await file.read()
            image = Image.open(io.BytesIO(image_data))
            processed_image = preprocess_image(image)
            
            # Make prediction
            predictions = model.predict(processed_image, verbose=0)
            prediction_probs = predictions[0]
            
            predicted_class_idx = np.argmax(prediction_probs)
            predicted_class = class_names[predicted_class_idx]
            confidence = float(prediction_probs[predicted_class_idx])
            
            class_probabilities = {
                class_names[i]: float(prediction_probs[i]) 
                for i in range(len(class_names))
            }
            
            results.append({
                "filename": file.filename,
                "success": True,
                "prediction": {
                    "class": predicted_class,
                    "confidence": confidence
                },
                "probabilities": class_probabilities
            })
            
        except Exception as e:
            results.append({
                "filename": file.filename,
                "success": False,
                "error": str(e)
            })
    
    return JSONResponse(content={
        "success": True,
        "results": results,
        "total_processed": len(results),
        "timestamp": datetime.now().isoformat()
    })

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
