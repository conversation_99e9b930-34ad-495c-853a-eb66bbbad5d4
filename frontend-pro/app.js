// Professional Foot Deformity Detection Frontend
// Advanced JavaScript with modern features and error handling

class FootDeformityApp {
    constructor() {
        this.apiBaseUrl = 'http://localhost:8000';
        this.selectedFile = null;
        this.isAnalyzing = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.checkApiHealth();
    }

    initializeElements() {
        // Upload elements
        this.uploadZone = document.getElementById('uploadZone');
        this.fileInput = document.getElementById('fileInput');
        this.selectedFileDiv = document.getElementById('selectedFile');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        
        // Progress elements
        this.progressContainer = document.getElementById('progressContainer');
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        
        // Button elements
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.analyzeText = document.getElementById('analyzeText');
        this.analyzeSpinner = document.getElementById('analyzeSpinner');
        this.resetBtn = document.getElementById('resetBtn');
        
        // Results elements
        this.placeholder = document.getElementById('placeholder');
        this.results = document.getElementById('results');
        this.resultIcon = document.getElementById('resultIcon');
        this.resultClass = document.getElementById('resultClass');
        this.resultConfidence = document.getElementById('resultConfidence');
        this.confidencePercent = document.getElementById('confidencePercent');
        this.confidenceBar = document.getElementById('confidenceBar');
        this.probabilitiesList = document.getElementById('probabilitiesList');
        this.recommendation = document.getElementById('recommendation');
        this.severityBadge = document.getElementById('severityBadge');
        this.processingTime = document.getElementById('processingTime');
        
        // Toast container
        this.toastContainer = document.getElementById('toastContainer');
    }

    setupEventListeners() {
        // File upload events
        this.uploadZone.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop events
        this.uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadZone.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Button events
        this.analyzeBtn.addEventListener('click', () => this.analyzeImage());
        this.resetBtn.addEventListener('click', () => this.resetAnalysis());
        
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });
    }

    async checkApiHealth() {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/health`, { timeout: 5000 });
            if (response.status === 200) {
                this.showToast('API connection established', 'success');
            }
        } catch (error) {
            this.showToast('API server not available. Please start the server.', 'error');
            this.analyzeBtn.disabled = true;
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadZone.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadZone.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    processFile(file) {
        // Validate file
        if (!this.validateFile(file)) {
            return;
        }

        this.selectedFile = file;
        this.displaySelectedFile(file);
        this.hideResults();
        this.showResetButton();
        this.showToast('Image uploaded successfully!', 'success');
    }

    validateFile(file) {
        // Check file type
        if (!file.type.startsWith('image/')) {
            this.showToast('Please upload an image file', 'error');
            return false;
        }

        // Check file size (10MB limit)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showToast('File size must be less than 10MB', 'error');
            return false;
        }

        return true;
    }

    displaySelectedFile(file) {
        this.fileName.textContent = file.name;
        this.fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
        this.selectedFileDiv.classList.remove('hidden');
        this.selectedFileDiv.classList.add('scale-in');
    }

    showResetButton() {
        this.resetBtn.classList.remove('hidden');
    }

    hideResults() {
        this.placeholder.classList.remove('hidden');
        this.results.classList.add('hidden');
    }

    async analyzeImage() {
        if (!this.selectedFile) {
            this.showToast('Please select an image first', 'error');
            return;
        }

        if (this.isAnalyzing) {
            return;
        }

        this.startAnalysis();

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);

            // Start progress simulation
            this.simulateProgress();

            const response = await axios.post(`${this.apiBaseUrl}/predict`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                timeout: 30000
            });

            this.completeAnalysis(response.data);

        } catch (error) {
            this.handleAnalysisError(error);
        }
    }

    startAnalysis() {
        this.isAnalyzing = true;
        this.analyzeBtn.disabled = true;
        this.analyzeText.classList.add('hidden');
        this.analyzeSpinner.classList.remove('hidden');
        this.progressContainer.classList.remove('hidden');
        this.resetBtn.disabled = true;
    }

    simulateProgress() {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) {
                progress = 90;
                clearInterval(interval);
            }
            this.updateProgress(progress);
        }, 200);

        // Store interval for cleanup
        this.progressInterval = interval;
    }

    updateProgress(progress) {
        this.progressBar.style.width = `${progress}%`;
        this.progressText.textContent = `${Math.round(progress)}%`;
    }

    completeAnalysis(data) {
        // Clear progress interval
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Complete progress
        this.updateProgress(100);

        setTimeout(() => {
            this.displayResults(data);
            this.endAnalysis();
            this.showToast('Analysis completed successfully!', 'success');
        }, 500);
    }

    handleAnalysisError(error) {
        this.endAnalysis();
        console.error('Analysis error:', error);

        let errorMessage = 'Analysis failed. Please try again.';

        if (error.code === 'ECONNABORTED') {
            errorMessage = 'Analysis timed out. Please try again.';
        } else if (error.response?.status === 413) {
            errorMessage = 'File too large. Please use a smaller image.';
        } else if (error.response?.status === 400) {
            errorMessage = 'Invalid file format. Please upload a valid image.';
        } else if (error.response?.status === 500) {
            errorMessage = 'Server error. Please check if the API server is running.';
        }

        this.showToast(errorMessage, 'error');
    }

    endAnalysis() {
        this.isAnalyzing = false;
        this.analyzeBtn.disabled = false;
        this.analyzeText.classList.remove('hidden');
        this.analyzeSpinner.classList.add('hidden');
        this.progressContainer.classList.add('hidden');
        this.resetBtn.disabled = false;
        this.updateProgress(0);
    }

    displayResults(data) {
        const prediction = data.prediction;
        const probabilities = data.probabilities;

        // Hide placeholder, show results
        this.placeholder.classList.add('hidden');
        this.results.classList.remove('hidden');

        // Display main result
        this.resultClass.textContent = prediction.class.replace('_', ' ');
        this.resultConfidence.textContent = `Confidence: ${(prediction.confidence * 100).toFixed(1)}%`;

        // Set result icon based on class
        this.setResultIcon(prediction.class, prediction.confidence);

        // Update confidence meter
        this.confidencePercent.textContent = `${(prediction.confidence * 100).toFixed(1)}%`;
        this.confidenceBar.style.width = `${prediction.confidence * 100}%`;
        this.setConfidenceColor(prediction.confidence);

        // Display detailed probabilities
        this.displayProbabilities(probabilities);

        // Display recommendation
        this.recommendation.textContent = prediction.recommendation;

        // Display severity badge
        if (prediction.severity) {
            this.displaySeverityBadge(prediction.severity);
        }

        // Display processing time
        if (data.processing_time) {
            this.processingTime.textContent = `Analysis completed in ${data.processing_time.toFixed(2)}s`;
        }
    }

    setResultIcon(className, confidence) {
        let iconSvg = '';
        let bgColor = 'bg-blue-100';

        if (className === 'normal') {
            bgColor = 'bg-green-100';
            iconSvg = `<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>`;
        } else {
            bgColor = confidence >= 0.8 ? 'bg-red-100' : 'bg-yellow-100';
            iconSvg = `<svg class="w-8 h-8 ${confidence >= 0.8 ? 'text-red-600' : 'text-yellow-600'}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>`;
        }

        this.resultIcon.className = `inline-flex items-center justify-center w-16 h-16 rounded-full ${bgColor} mb-4`;
        this.resultIcon.innerHTML = iconSvg;
    }

    setConfidenceColor(confidence) {
        let colorClass = 'bg-blue-500';
        if (confidence >= 0.8) {
            colorClass = 'bg-green-500';
        } else if (confidence >= 0.6) {
            colorClass = 'bg-yellow-500';
        } else {
            colorClass = 'bg-red-500';
        }
        
        this.confidenceBar.className = `confidence-fill ${colorClass}`;
    }

    displayProbabilities(probabilities) {
        this.probabilitiesList.innerHTML = '';
        
        Object.entries(probabilities).forEach(([className, probability]) => {
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between';
            
            div.innerHTML = `
                <span class="text-sm text-gray-600 capitalize">${className.replace('_', ' ')}</span>
                <div class="flex items-center space-x-2">
                    <div class="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div class="h-full bg-blue-500 rounded-full transition-all duration-800" style="width: ${probability * 100}%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900 w-12 text-right">${(probability * 100).toFixed(1)}%</span>
                </div>
            `;
            
            this.probabilitiesList.appendChild(div);
        });
    }

    displaySeverityBadge(severity) {
        let badgeClass = 'bg-blue-100 text-blue-800';
        
        if (severity.includes('Normal')) {
            badgeClass = 'bg-green-100 text-green-800';
        } else if (severity.includes('Mild')) {
            badgeClass = 'bg-yellow-100 text-yellow-800';
        } else {
            badgeClass = 'bg-red-100 text-red-800';
        }

        this.severityBadge.innerHTML = `
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${badgeClass}">
                ${severity}
            </span>
        `;
    }

    resetAnalysis() {
        this.selectedFile = null;
        this.selectedFileDiv.classList.add('hidden');
        this.resetBtn.classList.add('hidden');
        this.hideResults();
        this.fileInput.value = '';
        this.endAnalysis();
        this.showToast('Analysis reset', 'info');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5 transform transition-all duration-300 translate-x-full`;
        
        let iconColor = 'text-blue-500';
        let icon = 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
        
        if (type === 'success') {
            iconColor = 'text-green-500';
            icon = 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
        } else if (type === 'error') {
            iconColor = 'text-red-500';
            icon = 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z';
        }

        toast.innerHTML = `
            <div class="flex-1 w-0 p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 ${iconColor}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${icon}" />
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">${message}</p>
                    </div>
                </div>
            </div>
        `;

        this.toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Remove after 4 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 4000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FootDeformityApp();
});
