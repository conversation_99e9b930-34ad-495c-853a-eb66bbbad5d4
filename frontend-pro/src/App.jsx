import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useDropzone } from 'react-dropzone'
import { toast, Toaster } from 'react-hot-toast'
import axios from 'axios'
import {
  CloudArrowUpIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowPathIcon,
  InformationCircleIcon,
  HeartIcon,
  ShieldCheckIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'


const API_BASE_URL = 'http://localhost:8000'

function App() {
  const [selectedFile, setSelectedFile] = useState(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [results, setResults] = useState(null)
  const [uploadProgress, setUploadProgress] = useState(0)

  const onDrop = (acceptedFiles) => {
    const file = acceptedFiles[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error('File size must be less than 10MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        toast.error('Please upload an image file')
        return
      }

      setSelectedFile(file)
      setResults(null)
      toast.success('Image uploaded successfully!')
    }
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff']
    },
    multiple: false
  })

  const analyzeImage = async () => {
    if (!selectedFile) {
      toast.error('Please select an image first')
      return
    }

    setIsAnalyzing(true)
    setUploadProgress(0)

    const formData = new FormData()
    formData.append('file', selectedFile)

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      const response = await axios.post(`${API_BASE_URL}/predict`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      setTimeout(() => {
        setResults(response.data)
        setIsAnalyzing(false)
        setUploadProgress(0)
        toast.success('Analysis completed successfully!')
      }, 500)

    } catch (error) {
      setIsAnalyzing(false)
      setUploadProgress(0)
      console.error('Analysis error:', error)

      if (error.code === 'ECONNABORTED') {
        toast.error('Analysis timed out. Please try again.')
      } else if (error.response?.status === 413) {
        toast.error('File too large. Please use a smaller image.')
      } else if (error.response?.status === 400) {
        toast.error('Invalid file format. Please upload a valid image.')
      } else {
        toast.error('Analysis failed. Please ensure the API server is running.')
      }
    }
  }

  const resetAnalysis = () => {
    setSelectedFile(null)
    setResults(null)
    setIsAnalyzing(false)
    setUploadProgress(0)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />

      {/* Header */}
      <header className="bg-white shadow-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-medical-600 rounded-lg">
                <HeartIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">
                  Foot Deformity Detection
                </h1>
                <p className="text-sm text-slate-600">
                  AI-Powered Medical Imaging Analysis
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-slate-600">
                <ShieldCheckIcon className="w-4 h-4" />
                <span>HIPAA Compliant</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-slate-600">
                <ChartBarIcon className="w-4 h-4" />
                <span>86.11% Accuracy</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

          {/* Upload Section */}
          <div className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="medical-card"
            >
              <h2 className="text-xl font-semibold text-slate-900 mb-4">
                Upload X-Ray Image
              </h2>

              <div
                {...getRootProps()}
                className={`upload-zone cursor-pointer ${
                  isDragActive ? 'dragover' : ''
                }`}
              >
                <input {...getInputProps()} />
                <div className="space-y-4">
                  <CloudArrowUpIcon className="w-12 h-12 text-slate-400 mx-auto" />
                  <div>
                    <p className="text-lg font-medium text-slate-700">
                      {isDragActive
                        ? 'Drop the image here...'
                        : 'Drag & drop an X-ray image here'}
                    </p>
                    <p className="text-sm text-slate-500 mt-1">
                      or click to browse files
                    </p>
                  </div>
                  <div className="text-xs text-slate-400">
                    Supports: JPEG, PNG, BMP, TIFF (Max 10MB)
                  </div>
                </div>
              </div>

              {selectedFile && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mt-4 p-4 bg-slate-50 rounded-lg border border-slate-200"
                >
                  <div className="flex items-center space-x-3">
                    <DocumentIcon className="w-8 h-8 text-medical-600" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-slate-900 truncate">
                        {selectedFile.name}
                      </p>
                      <p className="text-xs text-slate-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <CheckCircleIcon className="w-5 h-5 text-success-600" />
                  </div>
                </motion.div>
              )}

              {/* Progress Bar */}
              {isAnalyzing && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mt-4"
                >
                  <div className="flex items-center justify-between text-sm text-slate-600 mb-2">
                    <span>Analyzing image...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="medical-progress-bar">
                    <motion.div
                      className="medical-progress-fill"
                      initial={{ width: 0 }}
                      animate={{ width: `${uploadProgress}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </motion.div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={analyzeImage}
                  disabled={!selectedFile || isAnalyzing}
                  className="medical-button-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAnalyzing ? (
                    <>
                      <ArrowPathIcon className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    'Analyze Image'
                  )}
                </button>

                {(selectedFile || results) && (
                  <button
                    onClick={resetAnalysis}
                    className="medical-button-secondary"
                    disabled={isAnalyzing}
                  >
                    Reset
                  </button>
                )}
              </div>
            </motion.div>

            {/* Information Panel */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="medical-card"
            >
              <div className="flex items-start space-x-3">
                <InformationCircleIcon className="w-6 h-6 text-medical-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-slate-900 mb-2">
                    How it works
                  </h3>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Upload a clear X-ray image of the foot</li>
                    <li>• Our AI model analyzes the image structure</li>
                    <li>• Get instant results with confidence scores</li>
                    <li>• Receive medical recommendations</li>
                  </ul>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            <AnimatePresence mode="wait">
              {results ? (
                <motion.div
                  key="results"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.5 }}
                  className="result-card"
                >
                  <h2 className="text-xl font-semibold text-slate-900 mb-6">
                    Analysis Results
                  </h2>

                  {/* Main Result */}
                  <div className="text-center mb-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-medical-100 mb-4">
                      {results.prediction.class === 'normal' ? (
                        <CheckCircleIcon className="w-8 h-8 text-success-600" />
                      ) : (
                        <ExclamationTriangleIcon className="w-8 h-8 text-warning-600" />
                      )}
                    </div>
                    <h3 className="text-2xl font-bold text-slate-900 capitalize mb-2">
                      {results.prediction.class.replace('_', ' ')}
                    </h3>
                    <p className="text-slate-600">
                      Confidence: {(results.prediction.confidence * 100).toFixed(1)}%
                    </p>
                  </div>

                  {/* Confidence Meter */}
                  <div className="mb-6">
                    <div className="flex justify-between text-sm text-slate-600 mb-2">
                      <span>Confidence Level</span>
                      <span>{(results.prediction.confidence * 100).toFixed(1)}%</span>
                    </div>
                    <div className="confidence-meter">
                      <motion.div
                        className={`confidence-fill ${
                          results.prediction.confidence >= 0.8
                            ? 'bg-success-500'
                            : results.prediction.confidence >= 0.6
                            ? 'bg-warning-500'
                            : 'bg-danger-500'
                        }`}
                        initial={{ width: 0 }}
                        animate={{ width: `${results.prediction.confidence * 100}%` }}
                        transition={{ duration: 1, delay: 0.5 }}
                      />
                    </div>
                  </div>

                  {/* Detailed Probabilities */}
                  <div className="space-y-3 mb-6">
                    <h4 className="font-medium text-slate-900">
                      Detailed Analysis
                    </h4>
                    {Object.entries(results.probabilities).map(([className, probability]) => (
                      <div key={className} className="flex items-center justify-between">
                        <span className="text-sm text-slate-600 capitalize">
                          {className.replace('_', ' ')}
                        </span>
                        <div className="flex items-center space-x-2">
                          <div className="w-24 h-2 bg-slate-200 rounded-full overflow-hidden">
                            <motion.div
                              className="h-full bg-medical-500 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ width: `${probability * 100}%` }}
                              transition={{ duration: 0.8, delay: 0.3 }}
                            />
                          </div>
                          <span className="text-sm font-medium text-slate-900 w-12 text-right">
                            {(probability * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Recommendations */}
                  <div className="bg-slate-50 rounded-lg p-4">
                    <h4 className="font-medium text-slate-900 mb-2">
                      Medical Recommendation
                    </h4>
                    <p className="text-sm text-slate-700">
                      {results.prediction.recommendation}
                    </p>

                    {results.prediction.severity && (
                      <div className="mt-3">
                        <span className={`medical-badge-${
                          results.prediction.severity.includes('Normal') ? 'success' :
                          results.prediction.severity.includes('Mild') ? 'warning' : 'danger'
                        }`}>
                          {results.prediction.severity}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Processing Time */}
                  <div className="mt-4 text-xs text-slate-500 text-center">
                    Analysis completed in {results.processing_time?.toFixed(2)}s
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="placeholder"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="medical-card text-center py-12"
                >
                  <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <ChartBarIcon className="w-8 h-8 text-slate-400" />
                  </div>
                  <h3 className="text-lg font-medium text-slate-900 mb-2">
                    Ready for Analysis
                  </h3>
                  <p className="text-slate-600">
                    Upload an X-ray image to get started with AI-powered diagnosis
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-slate-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <p className="text-sm text-slate-600">
              © 2024 Foot Deformity Detection System. Medical AI Technology.
            </p>
            <div className="flex items-center space-x-4 text-sm text-slate-600">
              <span>Model Accuracy: 86.11%</span>
              <span>•</span>
              <span>1.44M Parameters</span>
              <span>•</span>
              <span>Real-time Analysis</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
