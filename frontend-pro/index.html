<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foot Deformity Detection - Professional Medical AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .medical-gradient { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }
        .medical-card { background: white; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border: 1px solid #e2e8f0; }
        .upload-zone { border: 2px dashed #cbd5e1; border-radius: 1rem; padding: 2rem; text-align: center; transition: all 0.2s; background: #f8fafc; }
        .upload-zone:hover { border-color: #0ea5e9; background: #f0f9ff; }
        .upload-zone.dragover { border-color: #0ea5e9; background: #dbeafe; }
        .confidence-bar { height: 1rem; background: #e2e8f0; border-radius: 0.5rem; overflow: hidden; }
        .confidence-fill { height: 100%; border-radius: 0.5rem; transition: width 1s ease-out; }
        .medical-spinner { border: 2px solid #f3f4f6; border-top: 2px solid #0ea5e9; border-radius: 50%; width: 2rem; height: 2rem; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .scale-in { animation: scaleIn 0.3s ease-out; }
        @keyframes scaleIn { from { transform: scale(0.95); opacity: 0; } to { transform: scale(1); opacity: 1; } }
    </style>
</head>
<body class="medical-gradient min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Foot Deformity Detection</h1>
                        <p class="text-sm text-gray-600">AI-Powered Medical Imaging Analysis</p>
                    </div>
                </div>
                <div class="flex items-center space-x-6">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span>HIPAA Compliant</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span>86.11% Accuracy</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

            <!-- Upload Section -->
            <div class="space-y-6">
                <div class="medical-card p-6 fade-in">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Upload X-Ray Image</h2>

                    <!-- Upload Zone -->
                    <div id="uploadZone" class="upload-zone cursor-pointer">
                        <input type="file" id="fileInput" accept="image/*" class="hidden">
                        <div class="space-y-4">
                            <svg class="w-12 h-12 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div>
                                <p class="text-lg font-medium text-gray-700">Drag & drop an X-ray image here</p>
                                <p class="text-sm text-gray-500 mt-1">or click to browse files</p>
                            </div>
                            <div class="text-xs text-gray-400">Supports: JPEG, PNG, BMP, TIFF (Max 10MB)</div>
                        </div>
                    </div>

                    <!-- Selected File Display -->
                    <div id="selectedFile" class="hidden mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 scale-in">
                        <div class="flex items-center space-x-3">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <div class="flex-1 min-w-0">
                                <p id="fileName" class="text-sm font-medium text-gray-900 truncate"></p>
                                <p id="fileSize" class="text-xs text-gray-500"></p>
                            </div>
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div id="progressContainer" class="hidden mt-4">
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                            <span>Analyzing image...</span>
                            <span id="progressText">0%</span>
                        </div>
                        <div class="confidence-bar">
                            <div id="progressBar" class="confidence-fill bg-blue-600" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-3 mt-6">
                        <button id="analyzeBtn" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span id="analyzeText">Analyze Image</span>
                            <div id="analyzeSpinner" class="hidden medical-spinner mx-auto"></div>
                        </button>

                        <button id="resetBtn" class="hidden bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-all duration-200 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Reset
                        </button>
                    </div>
                </div>

                <!-- Information Panel -->
                <div class="medical-card p-6 fade-in">
                    <div class="flex items-start space-x-3">
                        <svg class="w-6 h-6 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-medium text-gray-900 mb-2">How it works</h3>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Upload a clear X-ray image of the foot</li>
                                <li>• Our AI model analyzes the image structure</li>
                                <li>• Get instant results with confidence scores</li>
                                <li>• Receive medical recommendations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="space-y-6">
                <div id="resultsContainer" class="medical-card p-6 fade-in">
                    <!-- Placeholder State -->
                    <div id="placeholder" class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Ready for Analysis</h3>
                        <p class="text-gray-600">Upload an X-ray image to get started with AI-powered diagnosis</p>
                    </div>

                    <!-- Results Display -->
                    <div id="results" class="hidden">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Analysis Results</h2>

                        <!-- Main Result -->
                        <div class="text-center mb-6">
                            <div id="resultIcon" class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 mb-4">
                                <!-- Icon will be inserted here -->
                            </div>
                            <h3 id="resultClass" class="text-2xl font-bold text-gray-900 capitalize mb-2"></h3>
                            <p id="resultConfidence" class="text-gray-600"></p>
                        </div>

                        <!-- Confidence Meter -->
                        <div class="mb-6">
                            <div class="flex justify-between text-sm text-gray-600 mb-2">
                                <span>Confidence Level</span>
                                <span id="confidencePercent"></span>
                            </div>
                            <div class="confidence-bar">
                                <div id="confidenceBar" class="confidence-fill" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Detailed Probabilities -->
                        <div class="space-y-3 mb-6">
                            <h4 class="font-medium text-gray-900">Detailed Analysis</h4>
                            <div id="probabilitiesList"></div>
                        </div>

                        <!-- Recommendations -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-900 mb-2">Medical Recommendation</h4>
                            <p id="recommendation" class="text-sm text-gray-700"></p>
                            <div id="severityBadge" class="mt-3"></div>
                        </div>

                        <!-- Processing Time -->
                        <div id="processingTime" class="mt-4 text-xs text-gray-500 text-center"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-600">© 2024 Foot Deformity Detection System. Medical AI Technology.</p>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Model Accuracy: 86.11%</span>
                    <span>•</span>
                    <span>1.44M Parameters</span>
                    <span>•</span>
                    <span>Real-time Analysis</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script src="app.js"></script>
</body>
</html>
