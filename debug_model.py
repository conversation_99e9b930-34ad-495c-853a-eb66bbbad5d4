#!/usr/bin/env python3
"""
Debug script to test the model and identify why it's always predicting flatfoot
"""

import os
import numpy as np
import tensorflow as tf
from PIL import Image
import cv2

def load_and_test_model():
    """Load the model and test it with different inputs"""
    
    print("🔍 DEBUGGING MODEL PREDICTIONS")
    print("="*50)
    
    # Try to load the model
    possible_paths = [
        "ml_model/saved_models/best_model.keras",
        "../ml_model/saved_models/best_model.keras",
        "ml_model/saved_models/final_model.keras",
        "../ml_model/saved_models/final_model.keras"
    ]
    
    model = None
    model_path = None
    
    for path in possible_paths:
        if os.path.exists(path):
            try:
                model = tf.keras.models.load_model(path)
                model_path = path
                print(f"✅ Model loaded from: {path}")
                break
            except Exception as e:
                print(f"❌ Failed to load from {path}: {e}")
                continue
    
    if model is None:
        print("❌ No model could be loaded!")
        return
    
    # Print model information
    print(f"\n📊 MODEL INFORMATION:")
    print(f"Input shape: {model.input_shape}")
    print(f"Output shape: {model.output_shape}")
    print(f"Total parameters: {model.count_params():,}")
    
    # Print model summary
    print(f"\n🏗️ MODEL ARCHITECTURE:")
    model.summary()
    
    # Test with different types of inputs
    class_names = ['flatfoot', 'hallux_valgus', 'normal']
    
    print(f"\n🧪 TESTING MODEL WITH DIFFERENT INPUTS:")
    print("-" * 40)
    
    # Test 1: Random noise
    print("Test 1: Random noise input")
    random_input = np.random.rand(1, 224, 224, 3)
    predictions = model.predict(random_input, verbose=0)
    predicted_class_idx = np.argmax(predictions[0])
    predicted_class = class_names[predicted_class_idx]
    confidence = predictions[0][predicted_class_idx]
    
    print(f"Predictions: {predictions[0]}")
    print(f"Predicted class: {predicted_class} (confidence: {confidence:.4f})")
    print(f"All probabilities: {dict(zip(class_names, predictions[0]))}")
    
    # Test 2: All zeros
    print("\nTest 2: All zeros input")
    zero_input = np.zeros((1, 224, 224, 3))
    predictions = model.predict(zero_input, verbose=0)
    predicted_class_idx = np.argmax(predictions[0])
    predicted_class = class_names[predicted_class_idx]
    confidence = predictions[0][predicted_class_idx]
    
    print(f"Predictions: {predictions[0]}")
    print(f"Predicted class: {predicted_class} (confidence: {confidence:.4f})")
    print(f"All probabilities: {dict(zip(class_names, predictions[0]))}")
    
    # Test 3: All ones
    print("\nTest 3: All ones input")
    ones_input = np.ones((1, 224, 224, 3))
    predictions = model.predict(ones_input, verbose=0)
    predicted_class_idx = np.argmax(predictions[0])
    predicted_class = class_names[predicted_class_idx]
    confidence = predictions[0][predicted_class_idx]
    
    print(f"Predictions: {predictions[0]}")
    print(f"Predicted class: {predicted_class} (confidence: {confidence:.4f})")
    print(f"All probabilities: {dict(zip(class_names, predictions[0]))}")
    
    # Test 4: Check if model weights are frozen or corrupted
    print(f"\n🔧 MODEL WEIGHTS ANALYSIS:")
    print("-" * 30)
    
    # Check if all weights are the same (indicating a problem)
    layer_weights = []
    for layer in model.layers:
        if len(layer.get_weights()) > 0:
            weights = layer.get_weights()[0]
            layer_weights.append(weights)
            print(f"Layer {layer.name}: Weight shape {weights.shape}, Mean: {np.mean(weights):.6f}, Std: {np.std(weights):.6f}")
    
    # Check if the final layer has reasonable weights
    final_layer = model.layers[-1]
    if len(final_layer.get_weights()) > 0:
        final_weights = final_layer.get_weights()[0]
        final_bias = final_layer.get_weights()[1] if len(final_layer.get_weights()) > 1 else None
        
        print(f"\n🎯 FINAL LAYER ANALYSIS:")
        print(f"Final layer weights shape: {final_weights.shape}")
        print(f"Final layer weights:\n{final_weights}")
        if final_bias is not None:
            print(f"Final layer bias: {final_bias}")
    
    # Test with actual image preprocessing
    print(f"\n🖼️ TESTING WITH SAMPLE IMAGES:")
    print("-" * 35)
    
    # Check if we have any test images
    test_dirs = [
        "ml_model/processed_dataset/test",
        "processed_dataset/test",
        "dataset/test"
    ]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            print(f"Found test directory: {test_dir}")
            
            # Test images from each class
            for class_name in class_names:
                class_dir = os.path.join(test_dir, class_name)
                if os.path.exists(class_dir):
                    images = [f for f in os.listdir(class_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    if images:
                        # Test first image from this class
                        image_path = os.path.join(class_dir, images[0])
                        print(f"\nTesting {class_name} image: {images[0]}")
                        
                        try:
                            # Load and preprocess image
                            image = Image.open(image_path).convert('RGB')
                            image = image.resize((224, 224))
                            image_array = np.array(image) / 255.0
                            image_array = np.expand_dims(image_array, axis=0)
                            
                            # Make prediction
                            predictions = model.predict(image_array, verbose=0)
                            predicted_class_idx = np.argmax(predictions[0])
                            predicted_class = class_names[predicted_class_idx]
                            confidence = predictions[0][predicted_class_idx]
                            
                            print(f"  Actual class: {class_name}")
                            print(f"  Predicted class: {predicted_class}")
                            print(f"  Confidence: {confidence:.4f}")
                            print(f"  All probabilities: {dict(zip(class_names, predictions[0]))}")
                            
                            # Check if prediction is correct
                            if predicted_class == class_name:
                                print("  ✅ CORRECT")
                            else:
                                print("  ❌ INCORRECT")
                                
                        except Exception as e:
                            print(f"  Error processing image: {e}")
            break
    
    print(f"\n🔍 DIAGNOSIS COMPLETE")
    print("="*50)
    
    return model

def check_training_data():
    """Check if training data is balanced"""
    print(f"\n📊 CHECKING TRAINING DATA BALANCE:")
    print("-" * 40)
    
    train_dirs = [
        "ml_model/processed_dataset/train",
        "processed_dataset/train",
        "dataset/train"
    ]
    
    class_names = ['flatfoot', 'hallux_valgus', 'normal']
    
    for train_dir in train_dirs:
        if os.path.exists(train_dir):
            print(f"Training directory: {train_dir}")
            
            for class_name in class_names:
                class_dir = os.path.join(train_dir, class_name)
                if os.path.exists(class_dir):
                    images = [f for f in os.listdir(class_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    print(f"  {class_name}: {len(images)} images")
            break

if __name__ == "__main__":
    print("🚀 Starting Model Diagnosis...")
    
    # Check training data balance
    check_training_data()
    
    # Load and test model
    model = load_and_test_model()
    
    print("\n💡 RECOMMENDATIONS:")
    print("1. Check if the model was trained properly")
    print("2. Verify training data is balanced")
    print("3. Check if model weights are reasonable")
    print("4. Consider retraining if issues are found")
