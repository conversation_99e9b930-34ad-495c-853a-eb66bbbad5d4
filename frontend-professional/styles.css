/* Professional Medical Frontend Styles */

/* Font Configuration */
.font-inter {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Medical UI Components */
.medical-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.medical-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

/* But<PERSON> Styles */
.medical-button-primary {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    font-weight: 600;
    padding: 0.875rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px -1px rgba(14, 165, 233, 0.3);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.medical-button-primary:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    box-shadow: 0 10px 15px -3px rgba(14, 165, 233, 0.4);
    transform: translateY(-1px);
}

.medical-button-primary:active {
    transform: translateY(0);
}

.medical-button-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.medical-button-secondary {
    background: #f8fafc;
    color: #475569;
    font-weight: 500;
    padding: 0.875rem 1.5rem;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    border: 1px solid #cbd5e1;
    cursor: pointer;
}

.medical-button-secondary:hover {
    background: #f1f5f9;
    border-color: #94a3b8;
    transform: translateY(-1px);
}

/* Upload Zone */
.upload-zone {
    border: 2px dashed #cbd5e1;
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
}

.upload-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-zone:hover {
    border-color: #0ea5e9;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.upload-zone:hover::before {
    opacity: 1;
}

.upload-zone.dragover {
    border-color: #0ea5e9;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    transform: scale(1.02);
}

.upload-zone.dragover::before {
    opacity: 1;
}

/* Progress and Confidence Bars */
.confidence-bar {
    height: 1rem;
    background: #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
    position: relative;
}

.confidence-fill {
    height: 100%;
    border-radius: 0.5rem;
    transition: width 1.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.confidence-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* Spinner Animation */
.medical-spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #0ea5e9;
    border-radius: 50%;
    width: 1.5rem;
    height: 1.5rem;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.4s ease-out;
}

/* Result Cards */
.result-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Badge Styles */
.medical-badge-success {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #86efac;
}

.medical-badge-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #fcd34d;
}

.medical-badge-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #fca5a5;
}

/* Toast Notifications */
.toast {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    padding: 1rem;
    max-width: 24rem;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .medical-card {
        padding: 1.5rem;
    }
    
    .upload-zone {
        padding: 2rem 1rem;
    }
    
    .medical-button-primary,
    .medical-button-secondary {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Focus States */
button:focus,
input:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}
