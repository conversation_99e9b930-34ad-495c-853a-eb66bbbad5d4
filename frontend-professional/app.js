// Professional Foot Deformity Detection Frontend
// Advanced JavaScript with modern ES6+ features and comprehensive error handling

class FootDeformityDetectionApp {
    constructor() {
        this.apiBaseUrl = 'http://localhost:8000';
        this.selectedFile = null;
        this.isAnalyzing = false;
        this.progressInterval = null;
        
        this.initializeElements();
        this.setupEventListeners();
        this.checkApiHealth();
        this.initializeAnimations();
    }

    initializeElements() {
        // Upload elements
        this.uploadZone = document.getElementById('uploadZone');
        this.fileInput = document.getElementById('fileInput');
        this.selectedFileDiv = document.getElementById('selectedFile');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        
        // Progress elements
        this.progressContainer = document.getElementById('progressContainer');
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        
        // Button elements
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.analyzeText = document.getElementById('analyzeText');
        this.analyzeSpinner = document.getElementById('analyzeSpinner');
        this.resetBtn = document.getElementById('resetBtn');
        
        // Results elements
        this.placeholder = document.getElementById('placeholder');
        this.results = document.getElementById('results');
        this.resultIcon = document.getElementById('resultIcon');
        this.resultClass = document.getElementById('resultClass');
        this.resultConfidence = document.getElementById('resultConfidence');
        this.confidencePercent = document.getElementById('confidencePercent');
        this.confidenceBar = document.getElementById('confidenceBar');
        this.probabilitiesList = document.getElementById('probabilitiesList');
        this.recommendation = document.getElementById('recommendation');
        this.severityBadge = document.getElementById('severityBadge');
        this.processingTime = document.getElementById('processingTime');
        
        // Toast container
        this.toastContainer = document.getElementById('toastContainer');
    }

    setupEventListeners() {
        // File upload events
        this.uploadZone.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop events
        this.uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadZone.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Button events
        this.analyzeBtn.addEventListener('click', () => this.analyzeImage());
        this.resetBtn.addEventListener('click', () => this.resetAnalysis());
        
        // Prevent default drag behaviors on document
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'u') {
                    e.preventDefault();
                    this.fileInput.click();
                } else if (e.key === 'Enter' && this.selectedFile && !this.isAnalyzing) {
                    e.preventDefault();
                    this.analyzeImage();
                }
            }
        });
    }

    initializeAnimations() {
        // Add entrance animations to cards
        const cards = document.querySelectorAll('.medical-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    async checkApiHealth() {
        try {
            const response = await axios.get(`${this.apiBaseUrl}/health`, { 
                timeout: 5000 
            });
            
            if (response.status === 200) {
                this.showToast('🟢 API connection established successfully', 'success');
                this.updateConnectionStatus(true);
            }
        } catch (error) {
            console.error('API Health Check Failed:', error);
            this.showToast('🔴 API server unavailable. Please start the server.', 'error');
            this.updateConnectionStatus(false);
            this.analyzeBtn.disabled = true;
        }
    }

    updateConnectionStatus(isConnected) {
        const statusElement = document.querySelector('.bg-green-50');
        if (statusElement) {
            if (isConnected) {
                statusElement.className = 'flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full';
                statusElement.innerHTML = `
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="font-medium">API Connected</span>
                `;
            } else {
                statusElement.className = 'flex items-center space-x-2 text-sm text-red-600 bg-red-50 px-3 py-1 rounded-full';
                statusElement.innerHTML = `
                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span class="font-medium">API Disconnected</span>
                `;
            }
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadZone.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        if (!this.uploadZone.contains(e.relatedTarget)) {
            this.uploadZone.classList.remove('dragover');
        }
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    processFile(file) {
        // Validate file
        const validation = this.validateFile(file);
        if (!validation.isValid) {
            this.showToast(`❌ ${validation.message}`, 'error');
            return;
        }

        this.selectedFile = file;
        this.displaySelectedFile(file);
        this.hideResults();
        this.showResetButton();
        this.showToast('✅ Image uploaded and validated successfully!', 'success');
    }

    validateFile(file) {
        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff'];
        if (!allowedTypes.includes(file.type.toLowerCase())) {
            return {
                isValid: false,
                message: 'Please upload a valid image file (JPEG, PNG, BMP, TIFF)'
            };
        }

        // Check file size (10MB limit)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            return {
                isValid: false,
                message: 'File size must be less than 10MB'
            };
        }

        // Check minimum file size (1KB)
        if (file.size < 1024) {
            return {
                isValid: false,
                message: 'File appears to be corrupted or too small'
            };
        }

        return { isValid: true };
    }

    displaySelectedFile(file) {
        this.fileName.textContent = file.name;
        this.fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
        this.selectedFileDiv.classList.remove('hidden');
        this.selectedFileDiv.classList.add('scale-in');
        
        // Enable analyze button
        this.analyzeBtn.disabled = false;
    }

    showResetButton() {
        this.resetBtn.classList.remove('hidden');
        this.resetBtn.classList.add('scale-in');
    }

    hideResults() {
        this.placeholder.classList.remove('hidden');
        this.results.classList.add('hidden');
    }

    async analyzeImage() {
        if (!this.selectedFile || this.isAnalyzing) {
            return;
        }

        this.startAnalysis();

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);

            // Start progress simulation
            this.simulateProgress();

            const response = await axios.post(`${this.apiBaseUrl}/predict`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                timeout: 30000,
                onUploadProgress: (progressEvent) => {
                    const uploadProgress = Math.round((progressEvent.loaded * 50) / progressEvent.total);
                    this.updateProgress(uploadProgress);
                }
            });

            this.completeAnalysis(response.data);

        } catch (error) {
            this.handleAnalysisError(error);
        }
    }

    startAnalysis() {
        this.isAnalyzing = true;
        this.analyzeBtn.disabled = true;
        this.analyzeText.classList.add('hidden');
        this.analyzeSpinner.classList.remove('hidden');
        this.progressContainer.classList.remove('hidden');
        this.resetBtn.disabled = true;
        
        // Add loading class to button
        this.analyzeBtn.classList.add('loading');
    }

    simulateProgress() {
        let progress = 0;
        this.progressInterval = setInterval(() => {
            progress += Math.random() * 10 + 5;
            if (progress > 85) {
                progress = 85;
                clearInterval(this.progressInterval);
            }
            this.updateProgress(progress);
        }, 300);
    }

    updateProgress(progress) {
        const clampedProgress = Math.min(Math.max(progress, 0), 100);
        this.progressBar.style.width = `${clampedProgress}%`;
        this.progressText.textContent = `${Math.round(clampedProgress)}%`;
        
        // Update progress bar color based on progress
        if (clampedProgress < 30) {
            this.progressBar.className = 'confidence-fill bg-gradient-to-r from-blue-400 to-blue-500';
        } else if (clampedProgress < 70) {
            this.progressBar.className = 'confidence-fill bg-gradient-to-r from-blue-500 to-blue-600';
        } else {
            this.progressBar.className = 'confidence-fill bg-gradient-to-r from-blue-600 to-blue-700';
        }
    }

    completeAnalysis(data) {
        // Clear progress interval
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        // Complete progress with animation
        this.updateProgress(100);

        setTimeout(() => {
            this.displayResults(data);
            this.endAnalysis();
            this.showToast('🎉 Analysis completed successfully!', 'success');
        }, 800);
    }

    handleAnalysisError(error) {
        this.endAnalysis();
        console.error('Analysis error:', error);

        let errorMessage = '❌ Analysis failed. Please try again.';
        let errorDetails = '';

        if (error.code === 'ECONNABORTED') {
            errorMessage = '⏱️ Analysis timed out. Please try again.';
            errorDetails = 'The request took too long to complete.';
        } else if (error.response?.status === 413) {
            errorMessage = '📁 File too large. Please use a smaller image.';
            errorDetails = 'Maximum file size is 10MB.';
        } else if (error.response?.status === 400) {
            errorMessage = '🖼️ Invalid file format. Please upload a valid image.';
            errorDetails = 'Supported formats: JPEG, PNG, BMP, TIFF.';
        } else if (error.response?.status === 500) {
            errorMessage = '🔧 Server error. Please check if the API server is running.';
            errorDetails = 'Contact support if the problem persists.';
        } else if (!navigator.onLine) {
            errorMessage = '🌐 No internet connection. Please check your network.';
            errorDetails = 'Make sure you are connected to the internet.';
        }

        this.showToast(errorMessage, 'error');
        
        // Show detailed error in console for debugging
        if (errorDetails) {
            console.warn('Error details:', errorDetails);
        }
    }

    endAnalysis() {
        this.isAnalyzing = false;
        this.analyzeBtn.disabled = false;
        this.analyzeText.classList.remove('hidden');
        this.analyzeSpinner.classList.add('hidden');
        this.progressContainer.classList.add('hidden');
        this.resetBtn.disabled = false;
        this.updateProgress(0);

        // Remove loading class
        this.analyzeBtn.classList.remove('loading');
    }

    displayResults(data) {
        const prediction = data.prediction;
        const probabilities = data.probabilities;

        // Hide placeholder, show results with animation
        this.placeholder.classList.add('hidden');
        this.results.classList.remove('hidden');
        this.results.classList.add('fade-in');

        // Display main result
        this.resultClass.textContent = prediction.class.replace('_', ' ');
        this.resultConfidence.textContent = `Confidence: ${(prediction.confidence * 100).toFixed(1)}%`;

        // Set result icon and styling based on class and confidence
        this.setResultIcon(prediction.class, prediction.confidence);

        // Update confidence meter with animation
        this.confidencePercent.textContent = `${(prediction.confidence * 100).toFixed(1)}%`;

        setTimeout(() => {
            this.confidenceBar.style.width = `${prediction.confidence * 100}%`;
            this.setConfidenceColor(prediction.confidence);
        }, 300);

        // Display detailed probabilities
        this.displayProbabilities(probabilities);

        // Display recommendation
        this.recommendation.textContent = prediction.recommendation;

        // Display severity badge if available
        if (prediction.severity) {
            this.displaySeverityBadge(prediction.severity);
        }

        // Display processing time
        if (data.processing_time) {
            this.processingTime.textContent = `⚡ Analysis completed in ${data.processing_time.toFixed(2)} seconds`;
        }

        // Scroll to results
        setTimeout(() => {
            this.results.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 500);
    }

    setResultIcon(className, confidence) {
        let iconSvg = '';
        let bgColor = 'bg-gradient-to-br from-blue-100 to-blue-200';
        let iconColor = 'text-blue-600';

        if (className === 'normal') {
            bgColor = 'bg-gradient-to-br from-green-100 to-green-200';
            iconColor = 'text-green-600';
            iconSvg = `<svg class="w-10 h-10 ${iconColor}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>`;
        } else {
            if (confidence >= 0.8) {
                bgColor = 'bg-gradient-to-br from-red-100 to-red-200';
                iconColor = 'text-red-600';
            } else {
                bgColor = 'bg-gradient-to-br from-yellow-100 to-yellow-200';
                iconColor = 'text-yellow-600';
            }
            iconSvg = `<svg class="w-10 h-10 ${iconColor}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>`;
        }

        this.resultIcon.className = `inline-flex items-center justify-center w-20 h-20 rounded-2xl mb-4 shadow-lg ${bgColor}`;
        this.resultIcon.innerHTML = iconSvg;
    }

    setConfidenceColor(confidence) {
        let colorClass = 'bg-gradient-to-r from-blue-500 to-blue-600';

        if (confidence >= 0.8) {
            colorClass = 'bg-gradient-to-r from-green-500 to-green-600';
        } else if (confidence >= 0.6) {
            colorClass = 'bg-gradient-to-r from-yellow-500 to-yellow-600';
        } else {
            colorClass = 'bg-gradient-to-r from-red-500 to-red-600';
        }

        this.confidenceBar.className = `confidence-fill ${colorClass}`;
    }

    displayProbabilities(probabilities) {
        this.probabilitiesList.innerHTML = '';

        // Sort probabilities by value (highest first)
        const sortedProbs = Object.entries(probabilities)
            .sort(([,a], [,b]) => b - a);

        sortedProbs.forEach(([className, probability], index) => {
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between p-3 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors';
            div.style.animationDelay = `${index * 0.1}s`;
            div.classList.add('slide-up');

            // Determine color based on probability
            let barColor = 'bg-slate-400';
            if (probability >= 0.7) {
                barColor = 'bg-green-500';
            } else if (probability >= 0.4) {
                barColor = 'bg-yellow-500';
            } else if (probability >= 0.2) {
                barColor = 'bg-orange-500';
            } else {
                barColor = 'bg-red-500';
            }

            div.innerHTML = `
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 ${barColor} rounded-full"></div>
                    <span class="text-sm font-medium text-slate-700 capitalize">${className.replace('_', ' ')}</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-24 h-2 bg-slate-200 rounded-full overflow-hidden">
                        <div class="h-full ${barColor} rounded-full transition-all duration-1000" style="width: ${probability * 100}%"></div>
                    </div>
                    <span class="text-sm font-bold text-slate-900 w-12 text-right">${(probability * 100).toFixed(1)}%</span>
                </div>
            `;

            this.probabilitiesList.appendChild(div);
        });
    }

    displaySeverityBadge(severity) {
        let badgeClass = 'medical-badge-success';
        let icon = '✅';

        if (severity.toLowerCase().includes('normal')) {
            badgeClass = 'medical-badge-success';
            icon = '✅';
        } else if (severity.toLowerCase().includes('mild')) {
            badgeClass = 'medical-badge-warning';
            icon = '⚠️';
        } else {
            badgeClass = 'medical-badge-danger';
            icon = '🚨';
        }

        this.severityBadge.innerHTML = `
            <span class="${badgeClass} flex items-center space-x-2">
                <span>${icon}</span>
                <span>${severity}</span>
            </span>
        `;
    }

    resetAnalysis() {
        // Reset all state
        this.selectedFile = null;
        this.selectedFileDiv.classList.add('hidden');
        this.resetBtn.classList.add('hidden');
        this.hideResults();
        this.fileInput.value = '';
        this.endAnalysis();

        // Reset button state
        this.analyzeBtn.disabled = true;

        // Show success message
        this.showToast('🔄 Analysis reset successfully', 'info');

        // Scroll back to upload section
        this.uploadZone.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = 'toast max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5';

        let iconColor = 'text-blue-500';
        let bgColor = 'bg-blue-50';
        let borderColor = 'border-blue-200';

        if (type === 'success') {
            iconColor = 'text-green-500';
            bgColor = 'bg-green-50';
            borderColor = 'border-green-200';
        } else if (type === 'error') {
            iconColor = 'text-red-500';
            bgColor = 'bg-red-50';
            borderColor = 'border-red-200';
        } else if (type === 'warning') {
            iconColor = 'text-yellow-500';
            bgColor = 'bg-yellow-50';
            borderColor = 'border-yellow-200';
        }

        toast.innerHTML = `
            <div class="flex-1 w-0 p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 ${bgColor} ${borderColor} border rounded-lg flex items-center justify-center">
                            <div class="w-4 h-4 ${iconColor}">
                                ${type === 'success' ? '✓' : type === 'error' ? '✗' : type === 'warning' ? '⚠' : 'ℹ'}
                            </div>
                        </div>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-slate-900">${message}</p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button class="toast-close bg-white rounded-md inline-flex text-slate-400 hover:text-slate-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <span class="sr-only">Close</span>
                            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => this.removeToast(toast));

        this.toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeToast(toast);
        }, 5000);
    }

    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Initializing Foot Deformity Detection App...');

    try {
        new FootDeformityDetectionApp();
        console.log('✅ Application initialized successfully');
    } catch (error) {
        console.error('❌ Failed to initialize application:', error);
    }
});

// Add global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

// Add unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
