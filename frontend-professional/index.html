<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foot Deformity Detection - Professional Medical AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦶</text></svg>">
</head>
<body class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 font-inter">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-slate-900">Foot Deformity Detection</h1>
                        <p class="text-sm text-slate-600">AI-Powered Medical Imaging Analysis</p>
                    </div>
                </div>
                <div class="flex items-center space-x-6">
                    <div class="flex items-center space-x-2 text-sm text-slate-600 bg-green-50 px-3 py-1 rounded-full">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="font-medium">API Connected</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-slate-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span>HIPAA Compliant</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-slate-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="font-semibold text-blue-600">86.11% Accuracy</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-slate-900 mb-4">
                Advanced AI Diagnosis for Foot Deformities
            </h2>
            <p class="text-xl text-slate-600 max-w-3xl mx-auto">
                Upload X-ray images and get instant, accurate analysis powered by deep learning technology. 
                Detect flatfoot, hallux valgus, and normal foot conditions with medical-grade precision.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Upload Section -->
            <div class="space-y-6">
                <div class="medical-card p-8 fade-in">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-slate-900">Upload X-Ray Image</h3>
                        <div class="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Ready</span>
                        </div>
                    </div>
                    
                    <!-- Upload Zone -->
                    <div id="uploadZone" class="upload-zone cursor-pointer group">
                        <input type="file" id="fileInput" accept="image/*" class="hidden">
                        <div class="space-y-6">
                            <div class="relative">
                                <svg class="w-16 h-16 text-slate-400 mx-auto group-hover:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <p class="text-lg font-medium text-slate-700 group-hover:text-slate-900 transition-colors">
                                    Drag & drop an X-ray image here
                                </p>
                                <p class="text-sm text-slate-500 mt-2">
                                    or click to browse files from your device
                                </p>
                            </div>
                            <div class="flex items-center justify-center space-x-4 text-xs text-slate-400">
                                <span class="flex items-center space-x-1">
                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span>JPEG, PNG, BMP, TIFF</span>
                                </span>
                                <span>•</span>
                                <span>Max 10MB</span>
                                <span>•</span>
                                <span>High Resolution Preferred</span>
                            </div>
                        </div>
                    </div>

                    <!-- Selected File Display -->
                    <div id="selectedFile" class="hidden mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 scale-in">
                        <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p id="fileName" class="text-sm font-semibold text-slate-900 truncate"></p>
                                <p id="fileSize" class="text-xs text-slate-600"></p>
                                <div class="flex items-center space-x-2 mt-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span class="text-xs text-green-600 font-medium">File validated</span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div id="progressContainer" class="hidden mt-6">
                        <div class="flex items-center justify-between text-sm text-slate-600 mb-3">
                            <div class="flex items-center space-x-2">
                                <div class="medical-spinner"></div>
                                <span class="font-medium">Analyzing image...</span>
                            </div>
                            <span id="progressText" class="font-semibold text-blue-600">0%</span>
                        </div>
                        <div class="confidence-bar">
                            <div id="progressBar" class="confidence-fill bg-gradient-to-r from-blue-500 to-blue-600" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-slate-500 mt-2 text-center">Processing with AI neural network...</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-4 mt-8">
                        <button id="analyzeBtn" class="flex-1 medical-button-primary group">
                            <span id="analyzeText" class="flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Analyze Image</span>
                            </span>
                            <div id="analyzeSpinner" class="hidden medical-spinner mx-auto"></div>
                        </button>
                        
                        <button id="resetBtn" class="hidden medical-button-secondary group">
                            <svg class="w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Reset
                        </button>
                    </div>
                </div>

                <!-- Information Panel -->
                <div class="medical-card p-6 fade-in">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-slate-900 mb-3">How it works</h4>
                            <ul class="text-sm text-slate-600 space-y-2">
                                <li class="flex items-start space-x-2">
                                    <span class="flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                                    <span>Upload a clear X-ray image of the foot</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <span class="flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                                    <span>Our AI model analyzes bone structure and alignment</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <span class="flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                                    <span>Get instant results with confidence scores</span>
                                </li>
                                <li class="flex items-start space-x-2">
                                    <span class="flex-shrink-0 w-1.5 h-1.5 bg-blue-500 rounded-full mt-2"></span>
                                    <span>Receive detailed medical recommendations</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="space-y-6">
                <div id="resultsContainer" class="medical-card p-8 fade-in">
                    <!-- Placeholder State -->
                    <div id="placeholder" class="text-center py-16">
                        <div class="relative">
                            <div class="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-inner">
                                <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-slate-900 mb-3">Ready for Analysis</h3>
                        <p class="text-slate-600 max-w-md mx-auto leading-relaxed">
                            Upload an X-ray image to get started with AI-powered diagnosis.
                            Our advanced neural network will analyze the image and provide detailed results.
                        </p>
                    </div>

                    <!-- Results Display -->
                    <div id="results" class="hidden">
                        <div class="flex items-center justify-between mb-8">
                            <h3 class="text-xl font-semibold text-slate-900">Analysis Results</h3>
                            <div class="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Analysis Complete</span>
                            </div>
                        </div>

                        <!-- Main Result -->
                        <div class="text-center mb-8 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100">
                            <div id="resultIcon" class="inline-flex items-center justify-center w-20 h-20 rounded-2xl mb-4 shadow-lg">
                                <!-- Icon will be inserted here -->
                            </div>
                            <h4 id="resultClass" class="text-3xl font-bold text-slate-900 capitalize mb-2"></h4>
                            <p id="resultConfidence" class="text-lg text-slate-600 font-medium"></p>
                        </div>

                        <!-- Confidence Meter -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center text-sm text-slate-600 mb-3">
                                <span class="font-medium">Confidence Level</span>
                                <span id="confidencePercent" class="font-bold text-slate-900"></span>
                            </div>
                            <div class="confidence-bar">
                                <div id="confidenceBar" class="confidence-fill" style="width: 0%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-slate-400 mt-2">
                                <span>Low</span>
                                <span>Medium</span>
                                <span>High</span>
                            </div>
                        </div>

                        <!-- Detailed Probabilities -->
                        <div class="space-y-4 mb-8">
                            <h5 class="font-semibold text-slate-900 flex items-center space-x-2">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Detailed Analysis</span>
                            </h5>
                            <div id="probabilitiesList" class="space-y-3"></div>
                        </div>

                        <!-- Recommendations -->
                        <div class="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-6 border border-slate-200">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <h5 class="font-semibold text-slate-900 mb-2">Medical Recommendation</h5>
                                    <p id="recommendation" class="text-sm text-slate-700 leading-relaxed mb-3"></p>
                                    <div id="severityBadge" class="flex items-center space-x-2"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Processing Time -->
                        <div id="processingTime" class="mt-6 text-center text-xs text-slate-500 bg-slate-50 rounded-lg py-2"></div>
                    </div>
                </div>

                <!-- Model Information -->
                <div class="medical-card p-6 fade-in">
                    <h4 class="font-semibold text-slate-900 mb-4 flex items-center space-x-2">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span>Model Information</span>
                    </h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-slate-600">Architecture:</span>
                                <span class="font-medium text-slate-900">CNN</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600">Parameters:</span>
                                <span class="font-medium text-slate-900">1.44M</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600">Accuracy:</span>
                                <span class="font-medium text-green-600">86.11%</span>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-slate-600">Classes:</span>
                                <span class="font-medium text-slate-900">3</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600">Input Size:</span>
                                <span class="font-medium text-slate-900">224×224</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-600">Framework:</span>
                                <span class="font-medium text-slate-900">TensorFlow</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-slate-200 mt-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <span class="font-bold text-slate-900">Foot Deformity AI</span>
                    </div>
                    <p class="text-sm text-slate-600">
                        Advanced medical AI technology for accurate foot deformity detection and analysis.
                    </p>
                </div>
                <div>
                    <h5 class="font-semibold text-slate-900 mb-3">Technology</h5>
                    <ul class="text-sm text-slate-600 space-y-1">
                        <li>• Deep Learning Neural Networks</li>
                        <li>• Medical Image Processing</li>
                        <li>• Real-time Analysis</li>
                        <li>• HIPAA Compliant Infrastructure</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold text-slate-900 mb-3">Performance</h5>
                    <ul class="text-sm text-slate-600 space-y-1">
                        <li>• Model Accuracy: <span class="font-semibold text-green-600">86.11%</span></li>
                        <li>• Processing Time: <span class="font-semibold">~1 second</span></li>
                        <li>• Supported Formats: JPEG, PNG, BMP, TIFF</li>
                        <li>• Maximum File Size: 10MB</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-slate-200 mt-8 pt-6 flex items-center justify-between">
                <p class="text-sm text-slate-600">
                    © 2024 Foot Deformity Detection System. Medical AI Technology.
                </p>
                <div class="flex items-center space-x-4 text-sm text-slate-600">
                    <span>Real-time Analysis</span>
                    <span>•</span>
                    <span>Medical Grade</span>
                    <span>•</span>
                    <span>AI Powered</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script src="app.js"></script>
</body>
</html>
