#!/usr/bin/env python3
"""
Update API to use the new balanced model
This script will update the API to use the balanced model once training is complete
"""

import os
import shutil
import requests
import time

def check_training_status():
    """Check if the balanced model training is complete"""
    
    balanced_model_path = "ml_model/saved_models/balanced_model.keras"
    final_model_path = "ml_model/saved_models/balanced_final_model.keras"
    
    if os.path.exists(final_model_path):
        print("✅ Final balanced model found!")
        return final_model_path
    elif os.path.exists(balanced_model_path):
        print("✅ Balanced model found!")
        return balanced_model_path
    else:
        print("⏳ Balanced model not ready yet...")
        return None

def backup_current_model():
    """Backup the current biased model"""
    
    current_model = "ml_model/saved_models/best_model.keras"
    backup_model = "ml_model/saved_models/biased_model_backup.keras"
    
    if os.path.exists(current_model):
        shutil.copy2(current_model, backup_model)
        print(f"📦 Backed up current model to: {backup_model}")
        return True
    else:
        print("❌ Current model not found!")
        return False

def update_model():
    """Update the API to use the new balanced model"""
    
    # Check if balanced model is ready
    balanced_model_path = check_training_status()
    
    if not balanced_model_path:
        print("❌ Balanced model not ready. Please wait for training to complete.")
        return False
    
    # Backup current model
    if not backup_current_model():
        print("❌ Failed to backup current model!")
        return False
    
    # Copy balanced model to replace the current one
    current_model_path = "ml_model/saved_models/best_model.keras"
    
    try:
        shutil.copy2(balanced_model_path, current_model_path)
        print(f"✅ Updated API model: {balanced_model_path} -> {current_model_path}")
        
        # Test if API is running and restart it
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("🔄 API is running. The new model will be loaded on next restart.")
                print("💡 Restart the API server to use the new balanced model:")
                print("   1. Stop the current API server (Ctrl+C)")
                print("   2. Run: cd api && python3 -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
            else:
                print("⚠️ API server not responding properly.")
        except:
            print("⚠️ API server not running. Start it to use the new model.")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to update model: {e}")
        return False

def test_new_model():
    """Test the new model with a simple prediction"""
    
    try:
        import tensorflow as tf
        import numpy as np
        from PIL import Image
        
        # Load the new model
        model_path = "ml_model/saved_models/best_model.keras"
        model = tf.keras.models.load_model(model_path)
        
        print(f"\n🧪 TESTING NEW MODEL")
        print("-" * 30)
        
        # Test with different inputs to verify it's not biased
        class_names = ['flatfoot', 'hallux_valgus', 'normal']
        
        # Test 1: Random input
        print("Test 1: Random input")
        random_input = np.random.rand(1, 224, 224, 3)
        predictions = model.predict(random_input, verbose=0)
        predicted_class = class_names[np.argmax(predictions[0])]
        confidence = np.max(predictions[0])
        
        print(f"  Predicted: {predicted_class} ({confidence:.3f})")
        print(f"  All probabilities: {dict(zip(class_names, predictions[0]))}")
        
        # Test 2: Zero input
        print("\nTest 2: Zero input")
        zero_input = np.zeros((1, 224, 224, 3))
        predictions = model.predict(zero_input, verbose=0)
        predicted_class = class_names[np.argmax(predictions[0])]
        confidence = np.max(predictions[0])
        
        print(f"  Predicted: {predicted_class} ({confidence:.3f})")
        print(f"  All probabilities: {dict(zip(class_names, predictions[0]))}")
        
        # Check if predictions are more balanced
        all_preds = [predictions[0] for _ in range(2)]
        avg_probs = np.mean(all_preds, axis=0)
        
        print(f"\n📊 Average probabilities across tests:")
        for i, class_name in enumerate(class_names):
            print(f"  {class_name}: {avg_probs[i]:.3f}")
        
        # Check if any class is heavily favored (>80%)
        max_prob = np.max(avg_probs)
        if max_prob > 0.8:
            dominant_class = class_names[np.argmax(avg_probs)]
            print(f"⚠️ Model still shows bias towards: {dominant_class} ({max_prob:.3f})")
        else:
            print("✅ Model appears more balanced!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    """Main function"""
    
    print("🔧 UPDATING API TO USE BALANCED MODEL")
    print("=" * 50)
    
    # Wait for training to complete if still running
    max_wait_time = 300  # 5 minutes
    wait_time = 0
    
    while wait_time < max_wait_time:
        balanced_model_path = check_training_status()
        
        if balanced_model_path:
            print(f"✅ Balanced model ready: {balanced_model_path}")
            break
        
        print(f"⏳ Waiting for training to complete... ({wait_time}s/{max_wait_time}s)")
        time.sleep(30)
        wait_time += 30
    
    if wait_time >= max_wait_time:
        print("❌ Timeout waiting for balanced model. Please run this script again after training completes.")
        return
    
    # Update the model
    if update_model():
        print("\n✅ MODEL UPDATE SUCCESSFUL!")
        
        # Test the new model
        test_new_model()
        
        print(f"\n🎉 NEXT STEPS:")
        print("1. Restart the API server to load the new balanced model")
        print("2. Test predictions with the frontend")
        print("3. Verify that all classes are predicted correctly")
        print("4. Monitor performance across different foot types")
        
    else:
        print("\n❌ MODEL UPDATE FAILED!")
        print("Please check the error messages above and try again.")

if __name__ == "__main__":
    main()
