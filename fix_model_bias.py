#!/usr/bin/env python3
"""
Fix Model Bias - Retrain with Balanced Data and Class Weights
This script addresses the model bias issue by:
1. Using class weights to balance training
2. Advanced data augmentation for minority classes
3. Improved training strategy
"""

import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, BatchNormalization, GlobalAveragePooling2D
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import matplotlib.pyplot as plt

def create_balanced_model():
    """Create an improved model architecture"""
    model = Sequential([
        # First Conv Block
        Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3), padding='same'),
        BatchNormalization(),
        Conv2D(32, (3, 3), activation='relu', padding='same'),
        MaxPooling2D((2, 2)),
        Dropout(0.25),
        
        # Second Conv Block
        Conv2D(64, (3, 3), activation='relu', padding='same'),
        BatchNormalization(),
        Conv2D(64, (3, 3), activation='relu', padding='same'),
        MaxPooling2D((2, 2)),
        Dropout(0.25),
        
        # Third Conv Block
        Conv2D(128, (3, 3), activation='relu', padding='same'),
        BatchNormalization(),
        Conv2D(128, (3, 3), activation='relu', padding='same'),
        MaxPooling2D((2, 2)),
        Dropout(0.25),
        
        # Fourth Conv Block
        Conv2D(256, (3, 3), activation='relu', padding='same'),
        BatchNormalization(),
        Conv2D(256, (3, 3), activation='relu', padding='same'),
        MaxPooling2D((2, 2)),
        Dropout(0.25),
        
        # Global Average Pooling instead of Flatten
        GlobalAveragePooling2D(),
        
        # Dense layers with proper regularization
        Dense(512, activation='relu'),
        BatchNormalization(),
        Dropout(0.5),
        
        Dense(256, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        
        # Output layer with proper initialization
        Dense(3, activation='softmax', kernel_initializer='he_normal')
    ])
    
    return model

def create_balanced_generators():
    """Create data generators with heavy augmentation for minority classes"""
    
    # Heavy augmentation for training (especially for flatfoot)
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=25,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=False,
        brightness_range=[0.8, 1.2],
        fill_mode='nearest'
    )
    
    # Minimal augmentation for validation
    val_datagen = ImageDataGenerator(rescale=1./255)
    
    # Create generators
    train_generator = train_datagen.flow_from_directory(
        'ml_model/processed_dataset/train',
        target_size=(224, 224),
        batch_size=32,
        class_mode='categorical',
        shuffle=True,
        seed=42
    )
    
    validation_generator = val_datagen.flow_from_directory(
        'ml_model/processed_dataset/validation',
        target_size=(224, 224),
        batch_size=32,
        class_mode='categorical',
        shuffle=False
    )
    
    return train_generator, validation_generator

def calculate_class_weights(train_generator):
    """Calculate class weights to handle imbalance"""
    
    # Get class distribution
    class_counts = {}
    for class_name, class_idx in train_generator.class_indices.items():
        class_dir = os.path.join('ml_model/processed_dataset/train', class_name)
        if os.path.exists(class_dir):
            count = len([f for f in os.listdir(class_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
            class_counts[class_idx] = count
    
    print("📊 Class Distribution:")
    for class_name, class_idx in train_generator.class_indices.items():
        print(f"  {class_name}: {class_counts[class_idx]} images")
    
    # Calculate weights (inverse frequency)
    total_samples = sum(class_counts.values())
    class_weights = {}
    
    for class_idx, count in class_counts.items():
        # Higher weight for minority classes
        weight = total_samples / (len(class_counts) * count)
        class_weights[class_idx] = weight
    
    print("\n⚖️ Class Weights (higher = more emphasis):")
    for class_name, class_idx in train_generator.class_indices.items():
        print(f"  {class_name}: {class_weights[class_idx]:.3f}")
    
    return class_weights

def train_balanced_model():
    """Train the model with balanced approach"""
    
    print("🚀 TRAINING BALANCED MODEL")
    print("="*50)
    
    # Create model
    model = create_balanced_model()
    
    # Compile with appropriate learning rate
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy', 'precision', 'recall']
    )
    
    print("🏗️ Model Architecture:")
    model.summary()
    
    # Create data generators
    train_gen, val_gen = create_balanced_generators()
    
    # Calculate class weights
    class_weights = calculate_class_weights(train_gen)
    
    # Create callbacks
    callbacks = [
        EarlyStopping(
            monitor='val_accuracy',
            patience=10,
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-7,
            verbose=1
        ),
        ModelCheckpoint(
            'ml_model/saved_models/balanced_model.keras',
            monitor='val_accuracy',
            save_best_only=True,
            verbose=1
        )
    ]
    
    # Train model
    print(f"\n🎯 Starting balanced training...")
    history = model.fit(
        train_gen,
        epochs=50,
        validation_data=val_gen,
        class_weight=class_weights,  # This is key for handling imbalance
        callbacks=callbacks,
        verbose=1
    )
    
    # Save final model
    model.save('ml_model/saved_models/balanced_final_model.keras')
    print("✅ Balanced model saved!")
    
    return model, history

def evaluate_balanced_model(model):
    """Evaluate the balanced model on test data"""
    
    print("\n🧪 EVALUATING BALANCED MODEL")
    print("="*40)
    
    # Create test generator
    test_datagen = ImageDataGenerator(rescale=1./255)
    test_generator = test_datagen.flow_from_directory(
        'ml_model/processed_dataset/test',
        target_size=(224, 224),
        batch_size=32,
        class_mode='categorical',
        shuffle=False
    )
    
    # Evaluate
    test_loss, test_accuracy, test_precision, test_recall = model.evaluate(test_generator, verbose=1)
    
    print(f"\n📊 Test Results:")
    print(f"  Accuracy: {test_accuracy:.4f}")
    print(f"  Precision: {test_precision:.4f}")
    print(f"  Recall: {test_recall:.4f}")
    print(f"  F1-Score: {2 * (test_precision * test_recall) / (test_precision + test_recall):.4f}")
    
    # Test individual classes
    print(f"\n🔍 Per-Class Performance:")
    
    class_names = ['flatfoot', 'hallux_valgus', 'normal']
    
    for class_name in class_names:
        class_dir = os.path.join('ml_model/processed_dataset/test', class_name)
        if os.path.exists(class_dir):
            # Test first few images from this class
            images = [f for f in os.listdir(class_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))][:3]
            
            correct_predictions = 0
            total_predictions = len(images)
            
            for image_file in images:
                image_path = os.path.join(class_dir, image_file)
                
                # Load and preprocess image
                from PIL import Image
                image = Image.open(image_path).convert('RGB')
                image = image.resize((224, 224))
                image_array = np.array(image) / 255.0
                image_array = np.expand_dims(image_array, axis=0)
                
                # Make prediction
                predictions = model.predict(image_array, verbose=0)
                predicted_class_idx = np.argmax(predictions[0])
                predicted_class = class_names[predicted_class_idx]
                confidence = predictions[0][predicted_class_idx]
                
                if predicted_class == class_name:
                    correct_predictions += 1
                
                print(f"  {class_name} -> {predicted_class} ({confidence:.3f}) {'✅' if predicted_class == class_name else '❌'}")
            
            accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
            print(f"  {class_name} accuracy: {accuracy:.3f} ({correct_predictions}/{total_predictions})")

def plot_training_history(history):
    """Plot training history"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Accuracy
    axes[0, 0].plot(history.history['accuracy'], label='Training')
    axes[0, 0].plot(history.history['val_accuracy'], label='Validation')
    axes[0, 0].set_title('Model Accuracy')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # Loss
    axes[0, 1].plot(history.history['loss'], label='Training')
    axes[0, 1].plot(history.history['val_loss'], label='Validation')
    axes[0, 1].set_title('Model Loss')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # Precision
    axes[1, 0].plot(history.history['precision'], label='Training')
    axes[1, 0].plot(history.history['val_precision'], label='Validation')
    axes[1, 0].set_title('Model Precision')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Precision')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # Recall
    axes[1, 1].plot(history.history['recall'], label='Training')
    axes[1, 1].plot(history.history['val_recall'], label='Validation')
    axes[1, 1].set_title('Model Recall')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Recall')
    axes[1, 1].legend()
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.savefig('ml_model/evaluation_results/balanced_training_history.png', dpi=300, bbox_inches='tight')
    print("📊 Training plots saved: ml_model/evaluation_results/balanced_training_history.png")

def main():
    """Main function"""
    
    print("🔧 FIXING MODEL BIAS ISSUE")
    print("="*60)
    print("Issues identified:")
    print("1. Class imbalance (flatfoot: 255, hallux_valgus: 609, normal: 288)")
    print("2. Model bias towards 'normal' and 'hallux_valgus'")
    print("3. Insufficient flatfoot representation")
    print("\nSolutions:")
    print("1. ⚖️ Class weights to balance training")
    print("2. 🔄 Heavy data augmentation")
    print("3. 🏗️ Improved model architecture")
    print("4. 📊 Better evaluation metrics")
    
    # Train balanced model
    model, history = train_balanced_model()
    
    # Evaluate model
    evaluate_balanced_model(model)
    
    # Plot results
    plot_training_history(history)
    
    print("\n🎉 BALANCED MODEL TRAINING COMPLETE!")
    print("✅ New model saved as: ml_model/saved_models/balanced_model.keras")
    print("✅ Final model saved as: ml_model/saved_models/balanced_final_model.keras")
    print("\n💡 Next steps:")
    print("1. Update API to use the new balanced model")
    print("2. Test with frontend to verify predictions")
    print("3. Monitor performance across all classes")

if __name__ == "__main__":
    main()
