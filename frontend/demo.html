<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Foot Deformity Detection System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .medical-card {
            @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-900">
                            Foot Deformity Detection System
                        </h1>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    Medical AI Assistant v1.0
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-4xl mx-auto py-6 px-4">
        <!-- Upload Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
                Upload Foot Image for Analysis
            </h2>
            
            <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer">
                <div id="uploadContent">
                    <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <p class="text-lg text-gray-600">
                        Drop your foot image here, or click to browse
                    </p>
                    <p class="text-sm text-gray-500 mt-1">
                        Supports JPEG, PNG, and other common image formats
                    </p>
                </div>
                <div id="previewContent" class="hidden">
                    <img id="previewImage" class="max-h-64 mx-auto rounded-lg shadow-sm mb-4" />
                    <p id="fileName" class="text-sm text-gray-600"></p>
                </div>
            </div>

            <input type="file" id="fileInput" accept="image/*" class="hidden">
            
            <div id="analyzeButton" class="mt-4 flex justify-center hidden">
                <button onclick="analyzeImage()" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Analyze Image
                </button>
            </div>
        </div>

        <!-- Loading Section -->
        <div id="loadingSection" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 hidden">
            <div class="flex items-center justify-center">
                <svg class="animate-spin h-8 w-8 text-blue-600 mr-3" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-lg text-gray-700">Analyzing image...</span>
            </div>
        </div>

        <!-- Error Section -->
        <div id="errorSection" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 hidden">
            <div class="flex">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Analysis Error</h3>
                    <p id="errorMessage" class="text-sm text-red-700 mt-1"></p>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6 hidden">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Analysis Results</h2>
            
            <div id="predictionCard" class="rounded-lg border p-4 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div id="classIcon"></div>
                        <div>
                            <h3 id="className" class="font-semibold text-lg capitalize"></h3>
                            <p id="confidence" class="text-sm opacity-75"></p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span id="severity" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"></span>
                    </div>
                </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h4 class="font-medium text-blue-900 mb-2">Medical Recommendation</h4>
                <p id="recommendation" class="text-blue-800"></p>
            </div>

            <div class="space-y-3">
                <h4 class="font-medium text-gray-900">Detailed Analysis</h4>
                <div id="probabilitiesContainer"></div>
            </div>
        </div>

        <!-- Medical Disclaimer -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Medical Disclaimer</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        This AI system is designed to assist medical professionals and should not be used as a 
                        substitute for professional medical diagnosis. Always consult with a qualified healthcare 
                        provider for proper medical evaluation and treatment.
                    </p>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                © 2024 Foot Deformity Detection System. For medical professional use only.
            </p>
        </div>
    </footer>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        let selectedFile = null;

        // Set up event listeners
        document.getElementById('dropZone').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        document.getElementById('dropZone').addEventListener('dragover', (e) => {
            e.preventDefault();
            e.currentTarget.classList.add('border-gray-400');
        });

        document.getElementById('dropZone').addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.currentTarget.classList.remove('border-gray-400');
        });

        document.getElementById('dropZone').addEventListener('drop', (e) => {
            e.preventDefault();
            e.currentTarget.classList.remove('border-gray-400');
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('image/')) {
                handleFileSelect(file);
            }
        });

        document.getElementById('fileInput').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFileSelect(file);
            }
        });

        function handleFileSelect(file) {
            selectedFile = file;
            
            // Show preview
            const reader = new FileReader();
            reader.onload = (e) => {
                document.getElementById('previewImage').src = e.target.result;
                document.getElementById('fileName').textContent = `${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                
                document.getElementById('uploadContent').classList.add('hidden');
                document.getElementById('previewContent').classList.remove('hidden');
                document.getElementById('analyzeButton').classList.remove('hidden');
            };
            reader.readAsDataURL(file);

            // Hide previous results
            hideResults();
        }

        async function analyzeImage() {
            if (!selectedFile) return;

            showLoading();
            hideError();
            hideResults();

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch(`${API_BASE_URL}/predict`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                showResults(result);
            } catch (error) {
                console.error('Prediction error:', error);
                showError('Failed to analyze the image. Please ensure the API server is running and try again.');
            } finally {
                hideLoading();
            }
        }

        function showLoading() {
            document.getElementById('loadingSection').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingSection').classList.add('hidden');
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorSection').classList.remove('hidden');
        }

        function hideError() {
            document.getElementById('errorSection').classList.add('hidden');
        }

        function showResults(result) {
            const prediction = result.prediction;
            
            // Update class name and confidence
            document.getElementById('className').textContent = prediction.class.replace('_', ' ');
            document.getElementById('confidence').textContent = `Confidence: ${(prediction.confidence * 100).toFixed(1)}%`;
            
            // Update severity and styling
            const severityElement = document.getElementById('severity');
            severityElement.textContent = prediction.severity;
            
            const predictionCard = document.getElementById('predictionCard');
            predictionCard.className = 'rounded-lg border p-4 mb-6 ' + getSeverityColor(prediction.severity);
            
            // Update icon
            document.getElementById('classIcon').innerHTML = getClassIcon(prediction.class);
            
            // Update recommendation
            document.getElementById('recommendation').textContent = prediction.recommendation;
            
            // Update probabilities
            const container = document.getElementById('probabilitiesContainer');
            container.innerHTML = '';
            
            Object.entries(result.probabilities).forEach(([className, probability]) => {
                const div = document.createElement('div');
                div.className = 'flex items-center justify-between';
                div.innerHTML = `
                    <span class="text-sm text-gray-600 capitalize">${className.replace('_', ' ')}</span>
                    <div class="flex items-center space-x-2">
                        <div class="w-32 bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: ${probability * 100}%"></div>
                        </div>
                        <span class="text-sm text-gray-900 w-12 text-right">${(probability * 100).toFixed(1)}%</span>
                    </div>
                `;
                container.appendChild(div);
            });
            
            document.getElementById('resultsSection').classList.remove('hidden');
        }

        function hideResults() {
            document.getElementById('resultsSection').classList.add('hidden');
        }

        function getSeverityColor(severity) {
            switch (severity.toLowerCase()) {
                case 'normal':
                    return 'text-green-600 bg-green-50 border-green-200';
                case 'mild to moderate':
                    return 'text-yellow-600 bg-yellow-50 border-yellow-200';
                case 'mild to severe':
                    return 'text-red-600 bg-red-50 border-red-200';
                default:
                    return 'text-gray-600 bg-gray-50 border-gray-200';
            }
        }

        function getClassIcon(className) {
            switch (className.toLowerCase()) {
                case 'normal':
                    return '<svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                case 'flatfoot':
                case 'hallux_valgus':
                    return '<svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                default:
                    return '<svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
            }
        }
    </script>
</body>
</html>
