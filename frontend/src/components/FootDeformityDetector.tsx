import React, { useState, useRef } from 'react';
import { Upload, Camera, AlertCircle, CheckCircle, Loader2, FileImage } from 'lucide-react';
import axios from 'axios';

interface PredictionResult {
  class: string;
  confidence: number;
  severity: string;
  recommendation: string;
}

interface PredictionResponse {
  success: boolean;
  prediction: PredictionResult;
  probabilities: Record<string, number>;
  metadata: {
    filename: string;
    image_size: string;
    timestamp: string;
  };
}

const FootDeformityDetector: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [prediction, setPrediction] = useState<PredictionResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const API_BASE_URL = 'http://localhost:8000';

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
      setPrediction(null);
      setError(null);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
      setPrediction(null);
      setError(null);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleAnalyze = async () => {
    if (!selectedFile) return;

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await axios.post<PredictionResponse>(
        `${API_BASE_URL}/predict`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      setPrediction(response.data);
    } catch (err) {
      console.error('Prediction error:', err);
      setError('Failed to analyze the image. Please ensure the API server is running and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'normal':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'mild to moderate':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'mild to severe':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getClassIcon = (className: string) => {
    switch (className.toLowerCase()) {
      case 'normal':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'flatfoot':
      case 'hallux_valgus':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Upload Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Upload Foot Image for Analysis
        </h2>
        
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          {previewUrl ? (
            <div className="space-y-4">
              <img
                src={previewUrl}
                alt="Preview"
                className="max-h-64 mx-auto rounded-lg shadow-sm"
              />
              <p className="text-sm text-gray-600">
                {selectedFile?.name} ({(selectedFile?.size || 0 / 1024 / 1024).toFixed(2)} MB)
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <FileImage className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <p className="text-lg text-gray-600">
                  Drop your foot image here, or click to browse
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  Supports JPEG, PNG, and other common image formats
                </p>
              </div>
            </div>
          )}
        </div>

        {selectedFile && (
          <div className="mt-4 flex justify-center">
            <button
              onClick={handleAnalyze}
              disabled={isLoading}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="animate-spin -ml-1 mr-3 h-5 w-5" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Camera className="-ml-1 mr-3 h-5 w-5" />
                  Analyze Image
                </>
              )}
            </button>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Analysis Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Results Section */}
      {prediction && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Analysis Results
          </h2>
          
          <div className="space-y-6">
            {/* Main Prediction */}
            <div className={`rounded-lg border p-4 ${getSeverityColor(prediction.prediction.severity)}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getClassIcon(prediction.prediction.class)}
                  <div>
                    <h3 className="font-semibold text-lg capitalize">
                      {prediction.prediction.class.replace('_', ' ')}
                    </h3>
                    <p className="text-sm opacity-75">
                      Confidence: {(prediction.prediction.confidence * 100).toFixed(1)}%
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {prediction.prediction.severity}
                  </span>
                </div>
              </div>
            </div>

            {/* Recommendation */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Medical Recommendation</h4>
              <p className="text-blue-800">{prediction.prediction.recommendation}</p>
            </div>

            {/* Detailed Probabilities */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Detailed Analysis</h4>
              {Object.entries(prediction.probabilities).map(([className, probability]) => (
                <div key={className} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">
                    {className.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${probability * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-900 w-12 text-right">
                      {(probability * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Metadata */}
            <div className="text-xs text-gray-500 border-t pt-4">
              <p>Analysis completed at: {new Date(prediction.metadata.timestamp).toLocaleString()}</p>
              <p>Image size: {prediction.metadata.image_size}</p>
            </div>
          </div>
        </div>
      )}

      {/* Medical Disclaimer */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-yellow-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Medical Disclaimer</h3>
            <p className="text-sm text-yellow-700 mt-1">
              This AI system is designed to assist medical professionals and should not be used as a 
              substitute for professional medical diagnosis. Always consult with a qualified healthcare 
              provider for proper medical evaluation and treatment.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FootDeformityDetector;
