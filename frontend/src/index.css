@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f9fafb;
}

/* Custom medical interface styles */
.medical-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.medical-button-primary {
  @apply inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.medical-button-secondary {
  @apply inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.medical-alert-success {
  @apply bg-green-50 border border-green-200 rounded-lg p-4;
}

.medical-alert-warning {
  @apply bg-yellow-50 border border-yellow-200 rounded-lg p-4;
}

.medical-alert-error {
  @apply bg-red-50 border border-red-200 rounded-lg p-4;
}

.medical-alert-info {
  @apply bg-blue-50 border border-blue-200 rounded-lg p-4;
}
