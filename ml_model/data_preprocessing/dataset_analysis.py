"""
Dataset Analysis Script for Foot Deformity Detection

This script analyzes the existing dataset structure and provides insights
about the data distribution, image properties, and class balance.
"""

import os
import csv
from collections import defaultdict
import json

class DatasetAnalyzer:
    def __init__(self, dataset_root="Dataset"):
        self.dataset_root = dataset_root
        self.analysis_results = {}
        
    def analyze_flatfoot_dataset(self):
        """Analyze the flatfoot dataset structure and annotations."""
        flatfoot_path = os.path.join(self.dataset_root, "Flatfoot", "Flatfoot detection.v1i.tensorflow")
        
        analysis = {
            "dataset_name": "Flatfoot Detection",
            "splits": {},
            "total_images": 0,
            "total_annotations": 0,
            "image_dimensions": [],
            "class_distribution": defaultdict(int)
        }
        
        for split in ["train", "test", "valid"]:
            split_path = os.path.join(flatfoot_path, split)
            if os.path.exists(split_path):
                # Count images
                images = [f for f in os.listdir(split_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                analysis["splits"][split] = len(images)
                analysis["total_images"] += len(images)
                
                # Analyze annotations if available
                annotations_file = os.path.join(split_path, "_annotations.csv")
                if os.path.exists(annotations_file):
                    with open(annotations_file, 'r') as f:
                        reader = csv.DictReader(f)
                        rows = list(reader)
                        analysis["total_annotations"] += len(rows)

                        # Class distribution and image dimensions
                        for row in rows:
                            class_name = row['class']
                            analysis["class_distribution"][class_name] += 1
                            width = int(row['width'])
                            height = int(row['height'])
                            analysis["image_dimensions"].append((width, height))
        
        return analysis
    
    def analyze_hallux_valgus_dataset(self):
        """Analyze the hallux valgus dataset structure and annotations."""
        hv_path = os.path.join(self.dataset_root, "Hallux Valgus", "Hallux Valgus.v8i.tensorflow")
        
        analysis = {
            "dataset_name": "Hallux Valgus Detection",
            "splits": {},
            "total_images": 0,
            "total_annotations": 0,
            "image_dimensions": [],
            "class_distribution": defaultdict(int),
            "severity_levels": defaultdict(int)
        }
        
        for split in ["train", "test", "valid"]:
            split_path = os.path.join(hv_path, split)
            if os.path.exists(split_path):
                # Count images
                images = [f for f in os.listdir(split_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                analysis["splits"][split] = len(images)
                analysis["total_images"] += len(images)
                
                # Analyze annotations if available
                annotations_file = os.path.join(split_path, "_annotations.csv")
                if os.path.exists(annotations_file):
                    with open(annotations_file, 'r') as f:
                        reader = csv.DictReader(f)
                        rows = list(reader)
                        analysis["total_annotations"] += len(rows)

                        # Class distribution and severity levels
                        for row in rows:
                            class_name = row['class']
                            analysis["class_distribution"][class_name] += 1
                            # Extract severity level (HV2, HV3, HV4)
                            if class_name.startswith('HV'):
                                analysis["severity_levels"][class_name] += 1

                            width = int(row['width'])
                            height = int(row['height'])
                            analysis["image_dimensions"].append((width, height))
        
        return analysis
    
    def create_classification_dataset_structure(self):
        """Create a new dataset structure for classification task."""
        # Define the new structure
        new_structure = {
            "processed_dataset": {
                "train": {
                    "normal": [],
                    "flatfoot": [],
                    "hallux_valgus": []
                },
                "validation": {
                    "normal": [],
                    "flatfoot": [],
                    "hallux_valgus": []
                },
                "test": {
                    "normal": [],
                    "flatfoot": [],
                    "hallux_valgus": []
                }
            }
        }
        
        # Create directories
        base_path = "ml_model/processed_dataset"
        for split in ["train", "validation", "test"]:
            for class_name in ["normal", "flatfoot", "hallux_valgus"]:
                dir_path = os.path.join(base_path, split, class_name)
                os.makedirs(dir_path, exist_ok=True)
        
        return new_structure
    
    def generate_analysis_report(self):
        """Generate a comprehensive analysis report."""
        print("=" * 60)
        print("FOOT DEFORMITY DATASET ANALYSIS REPORT")
        print("=" * 60)
        
        # Analyze flatfoot dataset
        flatfoot_analysis = self.analyze_flatfoot_dataset()
        print(f"\n{flatfoot_analysis['dataset_name']}:")
        print(f"  Total Images: {flatfoot_analysis['total_images']}")
        print(f"  Total Annotations: {flatfoot_analysis['total_annotations']}")
        print(f"  Data Splits: {flatfoot_analysis['splits']}")
        print(f"  Class Distribution: {dict(flatfoot_analysis['class_distribution'])}")
        
        # Analyze hallux valgus dataset
        hv_analysis = self.analyze_hallux_valgus_dataset()
        print(f"\n{hv_analysis['dataset_name']}:")
        print(f"  Total Images: {hv_analysis['total_images']}")
        print(f"  Total Annotations: {hv_analysis['total_annotations']}")
        print(f"  Data Splits: {hv_analysis['splits']}")
        print(f"  Class Distribution: {dict(hv_analysis['class_distribution'])}")
        print(f"  Severity Levels: {dict(hv_analysis['severity_levels'])}")
        
        # Image dimension analysis
        all_dimensions = flatfoot_analysis['image_dimensions'] + hv_analysis['image_dimensions']
        if all_dimensions:
            widths, heights = zip(*all_dimensions)
            mean_width = sum(widths) / len(widths)
            mean_height = sum(heights) / len(heights)
            print(f"\nImage Dimensions Analysis:")
            print(f"  Width - Min: {min(widths)}, Max: {max(widths)}, Mean: {mean_width:.1f}")
            print(f"  Height - Min: {min(heights)}, Max: {max(heights)}, Mean: {mean_height:.1f}")
        
        # Store results
        self.analysis_results = {
            "flatfoot": flatfoot_analysis,
            "hallux_valgus": hv_analysis,
            "image_dimensions": {
                "widths": list(widths) if all_dimensions else [],
                "heights": list(heights) if all_dimensions else []
            }
        }
        
        # Save analysis to file
        with open("ml_model/data_preprocessing/dataset_analysis_results.json", "w") as f:
            # Convert defaultdict to regular dict for JSON serialization
            results_copy = json.loads(json.dumps(self.analysis_results, default=lambda x: dict(x) if isinstance(x, defaultdict) else x))
            json.dump(results_copy, f, indent=2)
        
        print(f"\nAnalysis results saved to: ml_model/data_preprocessing/dataset_analysis_results.json")
        print("\nNext Steps:")
        print("1. Create normal foot images dataset (currently missing)")
        print("2. Reorganize data for classification task")
        print("3. Implement data preprocessing pipeline")
        print("4. Create train/validation/test splits")
        
        return self.analysis_results

if __name__ == "__main__":
    analyzer = DatasetAnalyzer()
    analyzer.generate_analysis_report()
    analyzer.create_classification_dataset_structure()
