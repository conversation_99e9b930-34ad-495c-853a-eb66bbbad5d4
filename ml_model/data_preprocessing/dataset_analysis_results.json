{"flatfoot": {"dataset_name": "Flatfoot Detection", "splits": {"train": 69, "test": 4, "valid": 9}, "total_images": 82, "total_annotations": 155, "image_dimensions": [[640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640], [640, 640]], "class_distribution": {"Flatfoot": 97, "Normal": 58}}, "hallux_valgus": {"dataset_name": "Hallux Valgus Detection", "splits": {"train": 609, "test": 26, "valid": 60}, "total_images": 695, "total_annotations": 695, "image_dimensions": [[392, 640], [384, 640], [480, 640], [450, 640], [435, 640], [368, 640], [432, 640], [393, 640], [426, 640], [565, 640], [457, 640], [385, 640], [435, 640], [488, 640], [378, 640], [398, 640], [529, 640], [366, 640], [471, 640], [406, 640], [483, 640], [406, 640], [449, 640], [406, 640], [449, 640], [496, 640], [370, 640], [459, 640], [360, 640], [390, 640], [445, 640], [415, 640], [370, 640], [472, 640], [371, 640], [537, 640], [478, 640], [300, 640], [423, 640], [536, 640], [436, 640], [377, 640], [318, 639], [374, 640], [437, 640], [450, 640], [380, 640], [562, 640], [450, 640], [362, 640], [258, 640], [283, 639], [349, 640], [389, 640], [381, 640], [417, 640], [364, 640], [420, 640], [541, 640], [423, 640], [431, 640], [409, 640], [390, 640], [444, 640], [428, 640], [365, 640], [253, 640], [445, 640], [396, 640], [373, 640], [572, 640], [424, 640], [389, 640], [446, 640], [483, 640], [397, 640], [420, 640], [387, 640], [471, 640], [341, 640], [437, 640], [467, 640], [392, 640], [443, 640], [496, 640], [529, 640], [572, 640], [357, 640], [307, 640], [466, 640], [379, 640], [459, 640], [390, 640], [384, 640], [451, 640], [432, 640], [431, 640], [485, 640], [480, 640], [395, 640], [482, 640], [412, 640], [480, 640], [387, 640], [585, 640], [440, 640], [480, 640], [438, 640], [429, 640], [480, 640], [385, 640], [387, 640], [453, 640], [640, 480], [376, 640], [449, 640], [373, 640], [396, 640], [365, 640], [396, 640], [437, 640], [356, 640], [373, 640], [459, 640], [421, 640], [387, 640], [422, 640], [471, 640], [410, 640], [572, 640], [488, 640], [431, 640], [483, 640], [370, 640], [480, 640], [389, 640], [318, 639], [541, 640], [445, 640], [508, 640], [410, 640], [484, 640], [397, 640], [372, 640], [349, 640], [398, 640], [388, 640], [392, 640], [466, 640], [437, 640], [444, 640], [426, 640], [395, 640], [409, 640], [417, 640], [407, 640], [389, 640], [384, 640], [441, 640], [477, 640], [368, 640], [442, 640], [436, 640], [542, 640], [371, 640], [487, 640], [422, 640], [531, 640], [419, 640], [548, 640], [504, 640], [483, 640], [556, 640], [597, 640], [415, 640], [442, 640], [392, 640], [519, 640], [497, 640], [488, 640], [451, 640], [434, 640], [387, 640], [395, 640], [424, 640], [394, 640], [348, 640], [378, 640], [487, 640], [565, 640], [442, 640], [508, 640], [422, 640], [584, 640], [597, 640], [494, 640], [379, 640], [391, 640], [372, 640], [412, 640], [389, 640], [499, 640], [397, 640], [391, 640], [480, 640], [354, 640], [508, 640], [477, 640], [421, 640], [392, 640], [410, 640], [397, 640], [508, 640], [446, 640], [435, 640], [494, 640], [484, 640], [392, 640], [384, 640], [450, 640], [398, 640], [354, 640], [437, 640], [389, 640], [365, 640], [447, 640], [407, 640], [426, 640], [437, 640], [397, 640], [414, 640], [441, 640], [453, 640], [366, 640], [580, 640], [390, 640], [444, 640], [393, 640], [398, 640], [442, 640], [332, 640], [381, 640], [452, 640], [542, 640], [531, 640], [417, 640], [341, 640], [480, 640], [371, 640], [365, 640], [290, 640], [445, 640], [453, 640], [258, 640], [394, 640], [381, 640], [478, 640], [420, 640], [565, 640], [253, 640], [640, 480], [213, 640], [213, 640], [368, 640], [391, 640], [411, 640], [377, 640], [536, 640], [374, 640], [442, 640], [420, 640], [445, 640], [413, 640], [541, 640], [483, 640], [390, 640], [332, 640], [432, 640], [378, 640], [328, 640], [374, 640], [551, 640], [414, 640], [445, 640], [434, 640], [365, 640], [428, 640], [378, 640], [369, 640], [457, 640], [348, 640], [424, 640], [421, 640], [409, 640], [557, 640], [452, 640], [478, 640], [356, 640], [476, 640], [584, 640], [387, 640], [504, 640], [447, 640], [389, 640], [437, 640], [408, 640], [472, 640], [423, 640], [395, 640], [414, 640], [400, 640], [450, 640], [409, 640], [442, 640], [374, 640], [421, 640], [389, 640], [471, 640], [387, 640], [483, 640], [390, 640], [443, 640], [415, 640], [423, 640], [471, 640], [438, 640], [443, 640], [420, 640], [429, 640], [436, 640], [376, 640], [371, 640], [389, 640], [450, 640], [410, 640], [436, 640], [341, 640], [395, 640], [380, 640], [566, 640], [392, 640], [420, 640], [466, 640], [423, 640], [258, 640], [420, 640], [472, 640], [453, 640], [364, 640], [562, 640], [640, 480], [519, 640], [377, 640], [283, 639], [392, 640], [464, 640], [349, 640], [420, 640], [362, 640], [566, 640], [318, 639], [466, 640], [417, 640], [419, 640], [408, 640], [387, 640], [488, 640], [488, 640], [451, 640], [421, 640], [414, 640], [348, 640], [381, 640], [419, 640], [408, 640], [354, 640], [412, 640], [537, 640], [464, 640], [385, 640], [397, 640], [253, 640], [354, 640], [365, 640], [360, 640], [379, 640], [469, 640], [397, 640], [420, 640], [432, 640], [408, 640], [446, 640], [419, 640], [395, 640], [300, 640], [424, 640], [562, 640], [354, 640], [437, 640], [537, 640], [418, 640], [389, 640], [365, 640], [440, 640], [480, 640], [360, 640], [467, 640], [307, 640], [476, 640], [378, 640], [480, 640], [415, 640], [459, 640], [434, 640], [585, 640], [419, 640], [504, 640], [469, 640], [390, 640], [452, 640], [290, 640], [357, 640], [397, 640], [432, 640], [471, 640], [480, 640], [369, 640], [480, 640], [328, 640], [536, 640], [424, 640], [395, 640], [585, 640], [409, 640], [413, 640], [332, 640], [377, 640], [397, 640], [412, 640], [435, 640], [400, 640], [435, 640], [438, 640], [363, 640], [580, 640], [357, 640], [356, 640], [332, 640], [420, 640], [432, 640], [480, 640], [413, 640], [409, 640], [387, 640], [393, 640], [363, 640], [410, 640], [409, 640], [376, 640], [384, 640], [435, 640], [398, 640], [480, 640], [459, 640], [480, 640], [429, 640], [453, 640], [421, 640], [391, 640], [366, 640], [411, 640], [467, 640], [389, 640], [374, 640], [408, 640], [477, 640], [494, 640], [447, 640], [508, 640], [414, 640], [397, 640], [397, 640], [487, 640], [480, 640], [508, 640], [372, 640], [365, 640], [597, 640], [584, 640], [354, 640], [392, 640], [391, 640], [409, 640], [551, 640], [480, 640], [388, 640], [436, 640], [415, 640], [442, 640], [332, 640], [422, 640], [428, 640], [389, 640], [422, 640], [466, 640], [499, 640], [548, 640], [424, 640], [408, 640], [369, 640], [415, 640], [413, 640], [413, 640], [556, 640], [551, 640], [484, 640], [408, 640], [378, 640], [300, 640], [442, 640], [410, 640], [332, 640], [290, 640], [213, 640], [398, 640], [419, 640], [283, 639], [436, 640], [364, 640], [529, 640], [556, 640], [580, 640], [457, 640], [377, 640], [496, 640], [531, 640], [412, 640], [499, 640], [459, 640], [409, 640], [446, 640], [435, 640], [418, 640], [466, 640], [453, 640], [381, 640], [390, 640], [420, 640], [328, 640], [407, 640], [394, 640], [423, 640], [390, 640], [418, 640], [497, 640], [397, 640], [409, 640], [381, 640], [413, 640], [391, 640], [412, 640], [435, 640], [437, 640], [371, 640], [414, 640], [476, 640], [411, 640], [482, 640], [557, 640], [374, 640], [548, 640], [480, 640], [409, 640], [417, 640], [363, 640], [408, 640], [362, 640], [400, 640], [365, 640], [485, 640], [488, 640], [408, 640], [482, 640], [409, 640], [377, 640], [542, 640], [420, 640], [440, 640], [422, 640], [446, 640], [388, 640], [497, 640], [417, 640], [441, 640], [464, 640], [557, 640], [566, 640], [371, 640], [446, 640], [380, 640], [469, 640], [442, 640], [435, 640], [307, 640], [384, 640], [519, 640], [485, 640], [395, 640], [395, 640], [385, 640], [370, 640], [507, 640], [382, 640], [462, 640], [377, 640], [346, 640], [466, 640], [382, 640], [420, 640], [391, 640], [530, 640], [391, 639], [363, 640], [389, 640], [492, 640], [547, 640], [467, 640], [349, 640], [417, 640], [400, 640], [422, 640], [378, 640], [388, 640], [450, 640], [563, 640], [558, 640], [279, 640], [473, 640], [375, 640], [323, 640], [506, 640], [366, 640], [368, 640], [434, 640], [226, 640], [544, 640], [546, 640], [396, 640], [397, 640], [354, 640], [404, 640], [516, 640], [394, 640], [398, 640], [382, 640], [379, 640], [425, 640], [445, 640], [215, 640], [539, 640], [500, 640], [406, 640], [518, 640], [373, 640], [463, 640], [358, 640], [448, 640], [402, 640], [386, 640], [396, 640], [303, 640], [412, 640], [475, 640], [491, 640], [463, 640], [387, 640], [312, 640], [398, 640], [381, 640], [357, 640], [423, 640], [371, 640], [373, 640], [449, 640], [501, 640], [479, 640], [468, 640], [443, 640], [374, 640], [355, 640], [412, 640], [596, 640], [442, 640], [433, 640], [519, 640]], "class_distribution": {"HV4": 127, "HV2": 187, "HV3": 172, "HV1": 209}, "severity_levels": {"HV4": 127, "HV2": 187, "HV3": 172, "HV1": 209}}, "image_dimensions": {"widths": [640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 392, 384, 480, 450, 435, 368, 432, 393, 426, 565, 457, 385, 435, 488, 378, 398, 529, 366, 471, 406, 483, 406, 449, 406, 449, 496, 370, 459, 360, 390, 445, 415, 370, 472, 371, 537, 478, 300, 423, 536, 436, 377, 318, 374, 437, 450, 380, 562, 450, 362, 258, 283, 349, 389, 381, 417, 364, 420, 541, 423, 431, 409, 390, 444, 428, 365, 253, 445, 396, 373, 572, 424, 389, 446, 483, 397, 420, 387, 471, 341, 437, 467, 392, 443, 496, 529, 572, 357, 307, 466, 379, 459, 390, 384, 451, 432, 431, 485, 480, 395, 482, 412, 480, 387, 585, 440, 480, 438, 429, 480, 385, 387, 453, 640, 376, 449, 373, 396, 365, 396, 437, 356, 373, 459, 421, 387, 422, 471, 410, 572, 488, 431, 483, 370, 480, 389, 318, 541, 445, 508, 410, 484, 397, 372, 349, 398, 388, 392, 466, 437, 444, 426, 395, 409, 417, 407, 389, 384, 441, 477, 368, 442, 436, 542, 371, 487, 422, 531, 419, 548, 504, 483, 556, 597, 415, 442, 392, 519, 497, 488, 451, 434, 387, 395, 424, 394, 348, 378, 487, 565, 442, 508, 422, 584, 597, 494, 379, 391, 372, 412, 389, 499, 397, 391, 480, 354, 508, 477, 421, 392, 410, 397, 508, 446, 435, 494, 484, 392, 384, 450, 398, 354, 437, 389, 365, 447, 407, 426, 437, 397, 414, 441, 453, 366, 580, 390, 444, 393, 398, 442, 332, 381, 452, 542, 531, 417, 341, 480, 371, 365, 290, 445, 453, 258, 394, 381, 478, 420, 565, 253, 640, 213, 213, 368, 391, 411, 377, 536, 374, 442, 420, 445, 413, 541, 483, 390, 332, 432, 378, 328, 374, 551, 414, 445, 434, 365, 428, 378, 369, 457, 348, 424, 421, 409, 557, 452, 478, 356, 476, 584, 387, 504, 447, 389, 437, 408, 472, 423, 395, 414, 400, 450, 409, 442, 374, 421, 389, 471, 387, 483, 390, 443, 415, 423, 471, 438, 443, 420, 429, 436, 376, 371, 389, 450, 410, 436, 341, 395, 380, 566, 392, 420, 466, 423, 258, 420, 472, 453, 364, 562, 640, 519, 377, 283, 392, 464, 349, 420, 362, 566, 318, 466, 417, 419, 408, 387, 488, 488, 451, 421, 414, 348, 381, 419, 408, 354, 412, 537, 464, 385, 397, 253, 354, 365, 360, 379, 469, 397, 420, 432, 408, 446, 419, 395, 300, 424, 562, 354, 437, 537, 418, 389, 365, 440, 480, 360, 467, 307, 476, 378, 480, 415, 459, 434, 585, 419, 504, 469, 390, 452, 290, 357, 397, 432, 471, 480, 369, 480, 328, 536, 424, 395, 585, 409, 413, 332, 377, 397, 412, 435, 400, 435, 438, 363, 580, 357, 356, 332, 420, 432, 480, 413, 409, 387, 393, 363, 410, 409, 376, 384, 435, 398, 480, 459, 480, 429, 453, 421, 391, 366, 411, 467, 389, 374, 408, 477, 494, 447, 508, 414, 397, 397, 487, 480, 508, 372, 365, 597, 584, 354, 392, 391, 409, 551, 480, 388, 436, 415, 442, 332, 422, 428, 389, 422, 466, 499, 548, 424, 408, 369, 415, 413, 413, 556, 551, 484, 408, 378, 300, 442, 410, 332, 290, 213, 398, 419, 283, 436, 364, 529, 556, 580, 457, 377, 496, 531, 412, 499, 459, 409, 446, 435, 418, 466, 453, 381, 390, 420, 328, 407, 394, 423, 390, 418, 497, 397, 409, 381, 413, 391, 412, 435, 437, 371, 414, 476, 411, 482, 557, 374, 548, 480, 409, 417, 363, 408, 362, 400, 365, 485, 488, 408, 482, 409, 377, 542, 420, 440, 422, 446, 388, 497, 417, 441, 464, 557, 566, 371, 446, 380, 469, 442, 435, 307, 384, 519, 485, 395, 395, 385, 370, 507, 382, 462, 377, 346, 466, 382, 420, 391, 530, 391, 363, 389, 492, 547, 467, 349, 417, 400, 422, 378, 388, 450, 563, 558, 279, 473, 375, 323, 506, 366, 368, 434, 226, 544, 546, 396, 397, 354, 404, 516, 394, 398, 382, 379, 425, 445, 215, 539, 500, 406, 518, 373, 463, 358, 448, 402, 386, 396, 303, 412, 475, 491, 463, 387, 312, 398, 381, 357, 423, 371, 373, 449, 501, 479, 468, 443, 374, 355, 412, 596, 442, 433, 519], "heights": [640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 639, 640, 640, 640, 640, 640, 640, 640, 640, 639, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 480, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 639, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 480, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 480, 640, 640, 639, 640, 640, 640, 640, 640, 640, 639, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 639, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 639, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640, 640]}}