"""
Data Reorganization Script for Foot Deformity Classification

This script reorganizes the object detection datasets into a classification format
with three classes: normal, flatfoot, and hallux_valgus.
"""

import os
import shutil
import csv
from collections import defaultdict
import json
from PIL import Image

class DataReorganizer:
    def __init__(self, source_root="Dataset", target_root="ml_model/processed_dataset"):
        self.source_root = source_root
        self.target_root = target_root
        self.class_mapping = {
            "Normal": "normal",
            "Flatfoot": "flatfoot",
            "HV1": "hallux_valgus",
            "HV2": "hallux_valgus", 
            "HV3": "hallux_valgus",
            "HV4": "hallux_valgus"
        }
        
    def create_directory_structure(self):
        """Create the target directory structure for classification."""
        splits = ["train", "validation", "test"]
        classes = ["normal", "flatfoot", "hallux_valgus"]
        
        for split in splits:
            for class_name in classes:
                dir_path = os.path.join(self.target_root, split, class_name)
                os.makedirs(dir_path, exist_ok=True)
                
        print(f"Created directory structure at: {self.target_root}")
    
    def process_flatfoot_dataset(self):
        """Process the flatfoot dataset and copy images to appropriate directories."""
        flatfoot_path = os.path.join(self.source_root, "Flatfoot", "Flatfoot detection.v1i.tensorflow")
        
        # Mapping from original splits to our splits
        split_mapping = {
            "train": "train",
            "valid": "validation", 
            "test": "test"
        }
        
        processed_count = defaultdict(lambda: defaultdict(int))
        
        for original_split, target_split in split_mapping.items():
            split_path = os.path.join(flatfoot_path, original_split)
            annotations_file = os.path.join(split_path, "_annotations.csv")
            
            if not os.path.exists(annotations_file):
                continue
                
            # Read annotations to get class labels
            image_classes = {}
            with open(annotations_file, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    filename = row['filename']
                    class_name = row['class']
                    if filename not in image_classes:
                        image_classes[filename] = set()
                    image_classes[filename].add(class_name)
            
            # Copy images to appropriate class directories
            for filename, classes in image_classes.items():
                source_path = os.path.join(split_path, filename)
                if not os.path.exists(source_path):
                    continue
                
                # Determine the primary class (if multiple classes, prioritize abnormal)
                if "Flatfoot" in classes:
                    target_class = "flatfoot"
                elif "Normal" in classes:
                    target_class = "normal"
                else:
                    continue
                
                target_dir = os.path.join(self.target_root, target_split, target_class)
                target_path = os.path.join(target_dir, f"flatfoot_{filename}")
                
                try:
                    shutil.copy2(source_path, target_path)
                    processed_count[target_split][target_class] += 1
                except Exception as e:
                    print(f"Error copying {filename}: {e}")
        
        return processed_count
    
    def process_hallux_valgus_dataset(self):
        """Process the hallux valgus dataset and copy images to appropriate directories."""
        hv_path = os.path.join(self.source_root, "Hallux Valgus", "Hallux Valgus.v8i.tensorflow")
        
        # Mapping from original splits to our splits
        split_mapping = {
            "train": "train",
            "valid": "validation",
            "test": "test"
        }
        
        processed_count = defaultdict(lambda: defaultdict(int))
        
        for original_split, target_split in split_mapping.items():
            split_path = os.path.join(hv_path, original_split)
            annotations_file = os.path.join(split_path, "_annotations.csv")
            
            if not os.path.exists(annotations_file):
                continue
                
            # Read annotations to get class labels
            image_classes = {}
            with open(annotations_file, 'r') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    filename = row['filename']
                    class_name = row['class']
                    if filename not in image_classes:
                        image_classes[filename] = set()
                    image_classes[filename].add(class_name)
            
            # Copy images to hallux_valgus directory (all HV classes map to hallux_valgus)
            for filename, classes in image_classes.items():
                source_path = os.path.join(split_path, filename)
                if not os.path.exists(source_path):
                    continue
                
                target_class = "hallux_valgus"
                target_dir = os.path.join(self.target_root, target_split, target_class)
                target_path = os.path.join(target_dir, f"hv_{filename}")
                
                try:
                    shutil.copy2(source_path, target_path)
                    processed_count[target_split][target_class] += 1
                except Exception as e:
                    print(f"Error copying {filename}: {e}")
        
        return processed_count
    
    def balance_dataset(self):
        """Analyze class distribution and suggest balancing strategies."""
        class_counts = defaultdict(lambda: defaultdict(int))
        
        splits = ["train", "validation", "test"]
        classes = ["normal", "flatfoot", "hallux_valgus"]
        
        for split in splits:
            for class_name in classes:
                class_dir = os.path.join(self.target_root, split, class_name)
                if os.path.exists(class_dir):
                    count = len([f for f in os.listdir(class_dir) if f.endswith(('.jpg', '.jpeg', '.png'))])
                    class_counts[split][class_name] = count
        
        return class_counts
    
    def generate_dataset_summary(self):
        """Generate a summary of the reorganized dataset."""
        class_counts = self.balance_dataset()
        
        print("\n" + "="*60)
        print("REORGANIZED DATASET SUMMARY")
        print("="*60)
        
        total_images = 0
        for split in ["train", "validation", "test"]:
            print(f"\n{split.upper()} SET:")
            split_total = 0
            for class_name in ["normal", "flatfoot", "hallux_valgus"]:
                count = class_counts[split][class_name]
                print(f"  {class_name}: {count} images")
                split_total += count
                total_images += count
            print(f"  Total: {split_total} images")
        
        print(f"\nOVERALL TOTAL: {total_images} images")
        
        # Class balance analysis
        print(f"\nCLASS BALANCE ANALYSIS:")
        for class_name in ["normal", "flatfoot", "hallux_valgus"]:
            total_class = sum(class_counts[split][class_name] for split in ["train", "validation", "test"])
            percentage = (total_class / total_images * 100) if total_images > 0 else 0
            print(f"  {class_name}: {total_class} images ({percentage:.1f}%)")
        
        # Save summary to file
        summary = {
            "class_counts": dict(class_counts),
            "total_images": total_images,
            "class_percentages": {
                class_name: sum(class_counts[split][class_name] for split in ["train", "validation", "test"]) / total_images * 100
                for class_name in ["normal", "flatfoot", "hallux_valgus"]
            } if total_images > 0 else {}
        }
        
        with open("ml_model/data_preprocessing/dataset_summary.json", "w") as f:
            json.dump(summary, f, indent=2, default=lambda x: dict(x) if isinstance(x, defaultdict) else x)
        
        return summary
    
    def reorganize_all(self):
        """Main method to reorganize all datasets."""
        print("Starting dataset reorganization...")
        
        # Create directory structure
        self.create_directory_structure()
        
        # Process flatfoot dataset
        print("\nProcessing Flatfoot dataset...")
        flatfoot_counts = self.process_flatfoot_dataset()
        print("Flatfoot dataset processed:")
        for split, classes in flatfoot_counts.items():
            for class_name, count in classes.items():
                print(f"  {split}/{class_name}: {count} images")
        
        # Process hallux valgus dataset
        print("\nProcessing Hallux Valgus dataset...")
        hv_counts = self.process_hallux_valgus_dataset()
        print("Hallux Valgus dataset processed:")
        for split, classes in hv_counts.items():
            for class_name, count in classes.items():
                print(f"  {split}/{class_name}: {count} images")
        
        # Generate summary
        summary = self.generate_dataset_summary()
        
        print(f"\nDataset reorganization complete!")
        print(f"Processed data saved to: {self.target_root}")
        print(f"Summary saved to: ml_model/data_preprocessing/dataset_summary.json")
        
        return summary

if __name__ == "__main__":
    reorganizer = DataReorganizer()
    reorganizer.reorganize_all()
