"""
Data Augmentation Pipeline for Foot Deformity Classification

This script handles class imbalance through data augmentation techniques
and prepares the data for CNN training.
"""

import os
import random
import shutil
from PIL import Image, ImageEnhance, ImageFilter
import json

class DataAugmenter:
    def __init__(self, dataset_root="ml_model/processed_dataset"):
        self.dataset_root = dataset_root
        self.target_size = (224, 224)  # Standard input size for CNN
        self.augmentation_techniques = [
            self.rotate_image,
            self.flip_horizontal,
            self.adjust_brightness,
            self.adjust_contrast,
            self.add_noise,
            self.slight_blur
        ]
    
    def load_image(self, image_path):
        """Load and preprocess an image."""
        try:
            image = Image.open(image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return image
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            return None
    
    def resize_image(self, image, target_size=None):
        """Resize image to target size while maintaining aspect ratio."""
        if target_size is None:
            target_size = self.target_size
        
        # Calculate aspect ratio preserving resize
        original_width, original_height = image.size
        target_width, target_height = target_size
        
        # Calculate scaling factor
        scale = min(target_width / original_width, target_height / original_height)
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        
        # Resize image
        image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Create new image with target size and paste resized image
        new_image = Image.new('RGB', target_size, (0, 0, 0))
        paste_x = (target_width - new_width) // 2
        paste_y = (target_height - new_height) // 2
        new_image.paste(image, (paste_x, paste_y))
        
        return new_image
    
    def rotate_image(self, image):
        """Rotate image by a random angle."""
        angle = random.uniform(-15, 15)
        return image.rotate(angle, fillcolor=(0, 0, 0))
    
    def flip_horizontal(self, image):
        """Flip image horizontally."""
        return image.transpose(Image.FLIP_LEFT_RIGHT)
    
    def adjust_brightness(self, image):
        """Adjust image brightness."""
        factor = random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Brightness(image)
        return enhancer.enhance(factor)
    
    def adjust_contrast(self, image):
        """Adjust image contrast."""
        factor = random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Contrast(image)
        return enhancer.enhance(factor)
    
    def add_noise(self, image):
        """Add slight noise to image."""
        # Convert to array, add noise, convert back
        import numpy as np
        img_array = np.array(image)
        noise = np.random.normal(0, 5, img_array.shape).astype(np.uint8)
        noisy_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        return Image.fromarray(noisy_array)
    
    def slight_blur(self, image):
        """Apply slight blur to image."""
        return image.filter(ImageFilter.GaussianBlur(radius=0.5))
    
    def augment_image(self, image, num_augmentations=1):
        """Apply random augmentations to an image."""
        augmented_images = []
        
        for _ in range(num_augmentations):
            aug_image = image.copy()
            
            # Apply 1-3 random augmentation techniques
            num_techniques = random.randint(1, 3)
            techniques = random.sample(self.augmentation_techniques, num_techniques)
            
            for technique in techniques:
                try:
                    aug_image = technique(aug_image)
                except Exception as e:
                    print(f"Error applying augmentation: {e}")
                    continue
            
            augmented_images.append(aug_image)
        
        return augmented_images
    
    def balance_dataset(self, target_count_per_class=500):
        """Balance the dataset by augmenting underrepresented classes."""
        print(f"Balancing dataset with target count: {target_count_per_class} per class")
        
        # Load current dataset summary
        summary_file = "ml_model/data_preprocessing/dataset_summary.json"
        with open(summary_file, 'r') as f:
            summary = json.load(f)
        
        class_counts = summary['class_counts']
        
        # Process each split
        for split in ['train']:  # Only augment training data
            print(f"\nProcessing {split} split...")
            
            for class_name in ['normal', 'flatfoot', 'hallux_valgus']:
                current_count = class_counts[split][class_name]
                needed_count = target_count_per_class - current_count
                
                if needed_count <= 0:
                    print(f"  {class_name}: {current_count} images (no augmentation needed)")
                    continue
                
                print(f"  {class_name}: {current_count} images, need {needed_count} more")
                
                # Get existing images
                class_dir = os.path.join(self.dataset_root, split, class_name)
                existing_images = [f for f in os.listdir(class_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
                
                if not existing_images:
                    print(f"    No images found in {class_dir}")
                    continue
                
                # Calculate augmentations per image
                augmentations_per_image = max(1, needed_count // len(existing_images))
                
                generated_count = 0
                for img_file in existing_images:
                    if generated_count >= needed_count:
                        break
                    
                    img_path = os.path.join(class_dir, img_file)
                    image = self.load_image(img_path)
                    
                    if image is None:
                        continue
                    
                    # Resize to standard size
                    image = self.resize_image(image)
                    
                    # Generate augmented images
                    augmented_images = self.augment_image(image, augmentations_per_image)
                    
                    # Save augmented images
                    base_name = os.path.splitext(img_file)[0]
                    for i, aug_img in enumerate(augmented_images):
                        if generated_count >= needed_count:
                            break
                        
                        aug_filename = f"{base_name}_aug_{i+1}.jpg"
                        aug_path = os.path.join(class_dir, aug_filename)
                        
                        try:
                            aug_img.save(aug_path, 'JPEG', quality=95)
                            generated_count += 1
                        except Exception as e:
                            print(f"    Error saving {aug_filename}: {e}")
                
                print(f"    Generated {generated_count} augmented images")
    
    def resize_all_images(self):
        """Resize all images to standard size."""
        print("Resizing all images to standard size...")
        
        splits = ['train', 'validation', 'test']
        classes = ['normal', 'flatfoot', 'hallux_valgus']
        
        total_processed = 0
        
        for split in splits:
            for class_name in classes:
                class_dir = os.path.join(self.dataset_root, split, class_name)
                if not os.path.exists(class_dir):
                    continue
                
                images = [f for f in os.listdir(class_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
                
                for img_file in images:
                    img_path = os.path.join(class_dir, img_file)
                    image = self.load_image(img_path)
                    
                    if image is None:
                        continue
                    
                    # Check if resize is needed
                    if image.size != self.target_size:
                        resized_image = self.resize_image(image)
                        try:
                            resized_image.save(img_path, 'JPEG', quality=95)
                            total_processed += 1
                        except Exception as e:
                            print(f"Error resizing {img_path}: {e}")
        
        print(f"Resized {total_processed} images")
    
    def generate_final_summary(self):
        """Generate final dataset summary after augmentation."""
        class_counts = {}
        splits = ['train', 'validation', 'test']
        classes = ['normal', 'flatfoot', 'hallux_valgus']
        
        total_images = 0
        
        for split in splits:
            class_counts[split] = {}
            for class_name in classes:
                class_dir = os.path.join(self.dataset_root, split, class_name)
                if os.path.exists(class_dir):
                    count = len([f for f in os.listdir(class_dir) if f.endswith(('.jpg', '.jpeg', '.png'))])
                    class_counts[split][class_name] = count
                    total_images += count
                else:
                    class_counts[split][class_name] = 0
        
        print("\n" + "="*60)
        print("FINAL DATASET SUMMARY (AFTER AUGMENTATION)")
        print("="*60)
        
        for split in splits:
            print(f"\n{split.upper()} SET:")
            split_total = sum(class_counts[split].values())
            for class_name in classes:
                count = class_counts[split][class_name]
                percentage = (count / split_total * 100) if split_total > 0 else 0
                print(f"  {class_name}: {count} images ({percentage:.1f}%)")
            print(f"  Total: {split_total} images")
        
        print(f"\nOVERALL TOTAL: {total_images} images")
        
        # Save final summary
        final_summary = {
            "class_counts": class_counts,
            "total_images": total_images,
            "target_size": self.target_size,
            "augmentation_applied": True
        }
        
        with open("ml_model/data_preprocessing/final_dataset_summary.json", "w") as f:
            json.dump(final_summary, f, indent=2)
        
        return final_summary
    
    def process_all(self, target_count_per_class=300):
        """Main method to process all data."""
        print("Starting data augmentation pipeline...")
        
        # First resize all existing images
        self.resize_all_images()
        
        # Balance dataset through augmentation
        self.balance_dataset(target_count_per_class)
        
        # Generate final summary
        summary = self.generate_final_summary()
        
        print(f"\nData augmentation complete!")
        print(f"Final summary saved to: ml_model/data_preprocessing/final_dataset_summary.json")
        
        return summary

if __name__ == "__main__":
    # Install numpy if not available
    try:
        import numpy as np
    except ImportError:
        print("Installing numpy...")
        os.system("pip3 install numpy")
        import numpy as np
    
    augmenter = DataAugmenter()
    augmenter.process_all(target_count_per_class=300)
