#!/usr/bin/env python3
"""
Model Evaluation Script
Evaluates the trained foot deformity classification model
"""

import os
import sys
import numpy as np
import tensorflow as tf
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
from pathlib import Path

def load_and_preprocess_image(image_path, target_size=(224, 224)):
    """Load and preprocess an image for prediction"""
    img = tf.keras.preprocessing.image.load_img(image_path, target_size=target_size)
    img_array = tf.keras.preprocessing.image.img_to_array(img)
    img_array = img_array / 255.0  # Normalize to [0,1]
    return img_array

def load_model(model_path):
    """Load the trained model"""
    try:
        model = tf.keras.models.load_model(model_path)
        print(f"✅ Model loaded successfully from {model_path}")
        return model
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def create_test_dataset(test_dir, img_size=(224, 224), batch_size=32):
    """Create test dataset"""
    test_ds = tf.keras.preprocessing.image_dataset_from_directory(
        test_dir,
        image_size=img_size,
        batch_size=batch_size,
        shuffle=False,
        label_mode='categorical'
    )
    
    # Normalize pixel values
    test_ds = test_ds.map(lambda x, y: (x / 255.0, y))
    
    return test_ds

def evaluate_model(model, test_ds, class_names):
    """Evaluate model performance"""
    print("\n" + "="*60)
    print("MODEL EVALUATION")
    print("="*60)
    
    # Get predictions
    predictions = model.predict(test_ds)
    predicted_classes = np.argmax(predictions, axis=1)
    
    # Get true labels
    true_labels = []
    for _, labels in test_ds:
        true_labels.extend(np.argmax(labels.numpy(), axis=1))
    
    true_labels = np.array(true_labels)
    
    # Calculate accuracy
    accuracy = np.mean(predicted_classes == true_labels)
    print(f"Test Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # Classification report
    print("\nClassification Report:")
    print(classification_report(true_labels, predicted_classes, target_names=class_names))
    
    # Confusion matrix
    cm = confusion_matrix(true_labels, predicted_classes)
    
    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap='Blues')
    plt.title('Confusion Matrix')
    plt.colorbar()

    # Add text annotations
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], 'd'),
                    horizontalalignment="center",
                    color="white" if cm[i, j] > thresh else "black")

    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.xticks(range(len(class_names)), class_names, rotation=45)
    plt.yticks(range(len(class_names)), class_names)
    plt.tight_layout()
    
    # Save confusion matrix
    os.makedirs('evaluation_results', exist_ok=True)
    plt.savefig('evaluation_results/confusion_matrix.png', dpi=300, bbox_inches='tight')
    print("✅ Confusion matrix saved to evaluation_results/confusion_matrix.png")
    
    return accuracy, predictions, predicted_classes, true_labels

def test_individual_predictions(model, test_dir, class_names, num_samples=5):
    """Test individual predictions"""
    print("\n" + "="*60)
    print("INDIVIDUAL PREDICTIONS TEST")
    print("="*60)
    
    for class_name in class_names:
        class_dir = os.path.join(test_dir, class_name)
        if os.path.exists(class_dir):
            images = [f for f in os.listdir(class_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            
            print(f"\n📁 Testing {class_name} samples:")
            for i, img_name in enumerate(images[:num_samples]):
                img_path = os.path.join(class_dir, img_name)
                
                # Load and preprocess image
                img = load_and_preprocess_image(img_path, target_size=(224, 224))
                img_batch = np.expand_dims(img, axis=0)
                
                # Make prediction
                prediction = model.predict(img_batch, verbose=0)
                predicted_class_idx = np.argmax(prediction)
                confidence = prediction[0][predicted_class_idx]
                predicted_class = class_names[predicted_class_idx]
                
                # Determine if prediction is correct
                is_correct = "✅" if predicted_class == class_name else "❌"
                
                print(f"  {is_correct} {img_name}: {predicted_class} ({confidence:.3f})")

def main():
    """Main evaluation function"""
    print("🔍 FOOT DEFORMITY MODEL EVALUATION")
    print("="*60)
    
    # Paths
    model_path = "saved_models/best_model.keras"
    test_dir = "processed_dataset/test"
    
    # Check if paths exist
    if not os.path.exists(model_path):
        print(f"❌ Model not found at {model_path}")
        return
    
    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found at {test_dir}")
        return
    
    # Load model
    model = load_model(model_path)
    if model is None:
        return
    
    # Print model summary
    print("\nModel Summary:")
    model.summary()
    
    # Class names (should match the directory names in test_dir)
    class_names = sorted([d for d in os.listdir(test_dir) 
                         if os.path.isdir(os.path.join(test_dir, d))])
    print(f"\nClasses found: {class_names}")
    
    # Create test dataset
    print("\n📊 Creating test dataset...")
    test_ds = create_test_dataset(test_dir)
    
    # Evaluate model
    accuracy, predictions, predicted_classes, true_labels = evaluate_model(model, test_ds, class_names)
    
    # Test individual predictions
    test_individual_predictions(model, test_dir, class_names)
    
    print("\n" + "="*60)
    print("EVALUATION COMPLETE")
    print("="*60)
    print(f"Final Test Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    if accuracy >= 0.85:
        print("🎯 Model meets the target accuracy of 85%!")
    else:
        print("⚠️  Model accuracy is below the target of 85%")

if __name__ == "__main__":
    main()
