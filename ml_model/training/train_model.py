"""
Training Script for Foot Deformity Classification CNN

This script handles the complete training pipeline including data loading,
model training, and evaluation.
"""

import os
import sys
import json
import numpy as np
import tensorflow as tf
from tensorflow import keras
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.cnn_model import FootDeformityCNN, create_callbacks

class ModelTrainer:
    def __init__(self, dataset_path="ml_model/processed_dataset"):
        self.dataset_path = dataset_path
        self.model = None
        self.history = None
        self.class_names = ['flatfoot', 'hallux_valgus', 'normal']  # Alphabetical order from flow_from_directory
        
        # Create necessary directories
        os.makedirs("ml_model/saved_models", exist_ok=True)
        os.makedirs("ml_model/training", exist_ok=True)
        os.makedirs("ml_model/evaluation", exist_ok=True)
    
    def create_data_generators(self, batch_size=32, image_size=(224, 224)):
        """Create data generators for training, validation, and testing."""
        
        # Training data generator with augmentation
        train_datagen = keras.preprocessing.image.ImageDataGenerator(
            rescale=1./255,
            rotation_range=15,
            width_shift_range=0.1,
            height_shift_range=0.1,
            shear_range=0.1,
            zoom_range=0.1,
            horizontal_flip=True,
            fill_mode='nearest'
        )
        
        # Validation and test data generators (no augmentation)
        val_test_datagen = keras.preprocessing.image.ImageDataGenerator(rescale=1./255)
        
        # Create generators
        train_generator = train_datagen.flow_from_directory(
            os.path.join(self.dataset_path, 'train'),
            target_size=image_size,
            batch_size=batch_size,
            class_mode='categorical',
            shuffle=True
        )
        
        validation_generator = val_test_datagen.flow_from_directory(
            os.path.join(self.dataset_path, 'validation'),
            target_size=image_size,
            batch_size=batch_size,
            class_mode='categorical',
            shuffle=False
        )
        
        test_generator = val_test_datagen.flow_from_directory(
            os.path.join(self.dataset_path, 'test'),
            target_size=image_size,
            batch_size=batch_size,
            class_mode='categorical',
            shuffle=False
        )
        
        print(f"Training samples: {train_generator.samples}")
        print(f"Validation samples: {validation_generator.samples}")
        print(f"Test samples: {test_generator.samples}")
        print(f"Class indices: {train_generator.class_indices}")
        
        return train_generator, validation_generator, test_generator
    
    def train_model(self, model_type='custom', epochs=50, batch_size=32, learning_rate=0.001):
        """Train the CNN model."""
        print(f"Starting training with {model_type} model...")
        
        # Create model
        cnn = FootDeformityCNN()
        self.model = cnn.create_model(model_type=model_type)
        cnn.compile_model(learning_rate=learning_rate)
        
        print("\nModel Summary:")
        self.model.summary()
        
        # Create data generators
        train_gen, val_gen, test_gen = self.create_data_generators(batch_size=batch_size)
        
        # Create callbacks
        callbacks = create_callbacks("ml_model/saved_models/best_model.keras")
        
        # Calculate steps per epoch
        steps_per_epoch = train_gen.samples // batch_size
        validation_steps = val_gen.samples // batch_size
        
        print(f"\nTraining configuration:")
        print(f"  Epochs: {epochs}")
        print(f"  Batch size: {batch_size}")
        print(f"  Learning rate: {learning_rate}")
        print(f"  Steps per epoch: {steps_per_epoch}")
        print(f"  Validation steps: {validation_steps}")
        
        # Train the model
        self.history = self.model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=val_gen,
            validation_steps=validation_steps,
            callbacks=callbacks,
            verbose=1
        )
        
        # Save final model
        self.model.save("ml_model/saved_models/final_model.keras")
        print("Training completed!")
        
        return self.history
    
    def evaluate_model(self):
        """Evaluate the trained model and generate comprehensive metrics."""
        if self.model is None:
            print("Loading best model for evaluation...")
            self.model = keras.models.load_model("ml_model/saved_models/best_model.h5")
        
        # Create test generator
        _, _, test_gen = self.create_data_generators(batch_size=1)  # Batch size 1 for accurate evaluation
        
        print("Evaluating model on test set...")
        
        # Get predictions
        test_gen.reset()
        predictions = self.model.predict(test_gen, steps=test_gen.samples, verbose=1)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # Get true labels
        true_classes = test_gen.classes
        
        # Calculate metrics
        test_loss, test_accuracy = self.model.evaluate(test_gen, steps=test_gen.samples, verbose=0)
        
        print(f"\nTest Results:")
        print(f"  Test Loss: {test_loss:.4f}")
        print(f"  Test Accuracy: {test_accuracy:.4f}")
        
        # Classification report
        class_report = classification_report(
            true_classes, 
            predicted_classes, 
            target_names=self.class_names,
            output_dict=True
        )
        
        print(f"\nClassification Report:")
        print(classification_report(true_classes, predicted_classes, target_names=self.class_names))
        
        # Confusion matrix
        cm = confusion_matrix(true_classes, predicted_classes)
        
        # Save evaluation results
        evaluation_results = {
            "test_loss": float(test_loss),
            "test_accuracy": float(test_accuracy),
            "classification_report": class_report,
            "confusion_matrix": cm.tolist(),
            "class_names": self.class_names
        }
        
        with open("ml_model/evaluation/evaluation_results.json", "w") as f:
            json.dump(evaluation_results, f, indent=2)
        
        # Plot confusion matrix
        self.plot_confusion_matrix(cm)
        
        # Plot training history if available
        if self.history is not None:
            self.plot_training_history()
        
        return evaluation_results
    
    def plot_confusion_matrix(self, cm):
        """Plot and save confusion matrix."""
        plt.figure(figsize=(8, 6))
        plt.imshow(cm, interpolation='nearest', cmap='Blues')
        plt.title('Confusion Matrix')
        plt.colorbar()

        # Add text annotations
        thresh = cm.max() / 2.
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                plt.text(j, i, format(cm[i, j], 'd'),
                        ha="center", va="center",
                        color="white" if cm[i, j] > thresh else "black")

        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        plt.xticks(range(len(self.class_names)), self.class_names)
        plt.yticks(range(len(self.class_names)), self.class_names)
        plt.tight_layout()
        plt.savefig('ml_model/evaluation/confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Confusion matrix saved to: ml_model/evaluation/confusion_matrix.png")
    
    def plot_training_history(self):
        """Plot and save training history."""
        if self.history is None:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Accuracy
        axes[0, 0].plot(self.history.history['accuracy'], label='Training Accuracy')
        axes[0, 0].plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 0].set_title('Model Accuracy')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Loss
        axes[0, 1].plot(self.history.history['loss'], label='Training Loss')
        axes[0, 1].plot(self.history.history['val_loss'], label='Validation Loss')
        axes[0, 1].set_title('Model Loss')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Precision
        if 'precision' in self.history.history:
            axes[1, 0].plot(self.history.history['precision'], label='Training Precision')
            axes[1, 0].plot(self.history.history['val_precision'], label='Validation Precision')
            axes[1, 0].set_title('Model Precision')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Precision')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # Recall
        if 'recall' in self.history.history:
            axes[1, 1].plot(self.history.history['recall'], label='Training Recall')
            axes[1, 1].plot(self.history.history['val_recall'], label='Validation Recall')
            axes[1, 1].set_title('Model Recall')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Recall')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('ml_model/evaluation/training_history.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Training history saved to: ml_model/evaluation/training_history.png")

def main():
    """Main training function."""
    print("="*60)
    print("FOOT DEFORMITY CLASSIFICATION - MODEL TRAINING")
    print("="*60)
    
    # Create trainer
    trainer = ModelTrainer()
    
    # Train model
    history = trainer.train_model(
        model_type='custom',  # or 'transfer_learning'
        epochs=30,  # Reduced for faster training
        batch_size=16,  # Smaller batch size for better convergence
        learning_rate=0.001
    )
    
    # Evaluate model
    results = trainer.evaluate_model()
    
    print("\n" + "="*60)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"Final Test Accuracy: {results['test_accuracy']:.4f}")
    print("Check ml_model/evaluation/ for detailed results and plots.")

if __name__ == "__main__":
    main()
