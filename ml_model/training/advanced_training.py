#!/usr/bin/env python3
"""
Advanced Model Training with State-of-the-Art Techniques
Implements transfer learning, advanced augmentation, and optimization strategies
"""

import os
import sys
import numpy as np
import tensorflow as tf
from tensorflow.keras.applications import EfficientNetB0, ResNet50V2, DenseNet121
from tensorflow.keras.layers import GlobalAveragePooling2D, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import AdamW
from tensorflow.keras.callbacks import ReduceLROnPlateau, EarlyStopping, ModelCheckpoint
from sklearn.utils.class_weight import compute_class_weight
import albumentations as A
import cv2
import matplotlib.pyplot as plt

class AdvancedFootDeformityTrainer:
    def __init__(self, data_dir="processed_dataset", img_size=(224, 224), batch_size=16):
        self.data_dir = data_dir
        self.img_size = img_size
        self.batch_size = batch_size
        self.num_classes = 3
        self.class_names = ['flatfoot', 'hallux_valgus', 'normal']
        
    def create_advanced_augmentation(self):
        """Create advanced augmentation pipeline using Albumentations"""
        return A.Compose([
            A.HorizontalFlip(p=0.5),
            A.RandomRotate90(p=0.3),
            A.Rotate(limit=15, p=0.5),
            A.RandomBrightnessContrast(
                brightness_limit=0.2, 
                contrast_limit=0.2, 
                p=0.5
            ),
            A.CLAHE(clip_limit=2.0, tile_grid_size=(8, 8), p=0.3),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.2),
            A.ElasticTransform(
                alpha=1, 
                sigma=50, 
                alpha_affine=50, 
                p=0.2
            ),
            A.GridDistortion(p=0.2),
            A.OpticalDistortion(distort_limit=0.1, shift_limit=0.1, p=0.2),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def create_transfer_learning_model(self, base_model_name="efficientnet"):
        """Create transfer learning model with different architectures"""
        
        # Choose base model
        if base_model_name == "efficientnet":
            base_model = EfficientNetB0(
                weights='imagenet',
                include_top=False,
                input_shape=(*self.img_size, 3)
            )
        elif base_model_name == "resnet":
            base_model = ResNet50V2(
                weights='imagenet',
                include_top=False,
                input_shape=(*self.img_size, 3)
            )
        elif base_model_name == "densenet":
            base_model = DenseNet121(
                weights='imagenet',
                include_top=False,
                input_shape=(*self.img_size, 3)
            )
        else:
            raise ValueError(f"Unknown base model: {base_model_name}")
        
        # Freeze base model initially
        base_model.trainable = False
        
        # Add custom head
        model = tf.keras.Sequential([
            base_model,
            GlobalAveragePooling2D(),
            BatchNormalization(),
            Dropout(0.3),
            Dense(512, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(256, activation='relu'),
            BatchNormalization(),
            Dropout(0.3),
            Dense(self.num_classes, activation='softmax')
        ])
        
        return model, base_model
    
    def create_advanced_callbacks(self, model_name):
        """Create advanced callbacks for training"""
        
        callbacks = [
            # Model checkpointing
            ModelCheckpoint(
                f'saved_models/{model_name}_best.keras',
                monitor='val_accuracy',
                save_best_only=True,
                mode='max',
                verbose=1,
                save_weights_only=False
            ),
            
            # Learning rate scheduling
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.3,
                patience=3,
                min_lr=1e-7,
                verbose=1,
                cooldown=1
            ),
            
            # Early stopping
            EarlyStopping(
                monitor='val_accuracy',
                patience=8,
                restore_best_weights=True,
                verbose=1,
                mode='max'
            ),
            
            # Custom callback for unfreezing layers
            tf.keras.callbacks.LambdaCallback(
                on_epoch_end=lambda epoch, logs: self.unfreeze_callback(epoch)
            )
        ]
        
        return callbacks
    
    def unfreeze_callback(self, epoch):
        """Gradually unfreeze layers during training"""
        if hasattr(self, 'base_model') and epoch == 5:
            print("\n🔓 Unfreezing top layers of base model...")
            # Unfreeze top 20% of layers
            total_layers = len(self.base_model.layers)
            unfreeze_from = int(total_layers * 0.8)
            
            for layer in self.base_model.layers[unfreeze_from:]:
                layer.trainable = True
            
            # Recompile with lower learning rate
            self.model.compile(
                optimizer=AdamW(learning_rate=1e-5, weight_decay=1e-4),
                loss='categorical_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
            print(f"✅ Unfroze {total_layers - unfreeze_from} layers")
    
    def create_data_generators(self):
        """Create data generators with advanced augmentation"""
        
        # Training generator with heavy augmentation
        train_datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            preprocessing_function=self.apply_albumentations,
            horizontal_flip=True,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            zoom_range=0.2,
            shear_range=0.1,
            fill_mode='nearest'
        )
        
        # Validation generator with minimal augmentation
        val_datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rescale=1./255
        )
        
        train_generator = train_datagen.flow_from_directory(
            os.path.join(self.data_dir, 'train'),
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='categorical',
            shuffle=True
        )
        
        val_generator = val_datagen.flow_from_directory(
            os.path.join(self.data_dir, 'validation'),
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='categorical',
            shuffle=False
        )
        
        return train_generator, val_generator
    
    def apply_albumentations(self, image):
        """Apply Albumentations augmentation"""
        try:
            # Convert to uint8 if needed
            if image.dtype != np.uint8:
                image = (image * 255).astype(np.uint8)
            
            # Apply augmentation
            augmented = self.augmentation_pipeline(image=image)
            return augmented['image']
        except:
            # Fallback to simple normalization
            return image / 255.0
    
    def calculate_class_weights(self, train_generator):
        """Calculate balanced class weights"""
        
        # Get all labels
        labels = []
        for i in range(len(train_generator)):
            _, batch_labels = train_generator[i]
            labels.extend(np.argmax(batch_labels, axis=1))
        
        # Calculate weights
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(labels),
            y=labels
        )
        
        class_weight_dict = {i: weight for i, weight in enumerate(class_weights)}
        
        print("📊 Class weights:")
        for i, (class_name, weight) in enumerate(zip(self.class_names, class_weights)):
            print(f"   {class_name}: {weight:.3f}")
        
        return class_weight_dict
    
    def train_advanced_model(self, base_model_name="efficientnet", epochs=30):
        """Train advanced model with transfer learning"""
        
        print(f"🚀 ADVANCED TRAINING - {base_model_name.upper()}")
        print("="*60)
        
        # Create augmentation pipeline
        self.augmentation_pipeline = self.create_advanced_augmentation()
        
        # Create model
        self.model, self.base_model = self.create_transfer_learning_model(base_model_name)
        
        # Compile model
        self.model.compile(
            optimizer=AdamW(learning_rate=1e-3, weight_decay=1e-4),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        print(f"\n🏗️ Model Architecture ({base_model_name}):")
        self.model.summary()
        
        # Create data generators
        train_gen, val_gen = self.create_data_generators()
        
        # Calculate class weights
        class_weights = self.calculate_class_weights(train_gen)
        
        # Create callbacks
        callbacks = self.create_advanced_callbacks(f"advanced_{base_model_name}")
        
        # Train model
        print(f"\n🎯 Starting training for {epochs} epochs...")
        history = self.model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            class_weight=class_weights,
            callbacks=callbacks,
            verbose=1
        )
        
        # Save final model
        final_model_path = f'saved_models/advanced_{base_model_name}_final.keras'
        self.model.save(final_model_path)
        print(f"✅ Final model saved: {final_model_path}")
        
        return history
    
    def plot_training_results(self, history, model_name):
        """Plot comprehensive training results"""
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Accuracy
        axes[0, 0].plot(history.history['accuracy'], label='Training')
        axes[0, 0].plot(history.history['val_accuracy'], label='Validation')
        axes[0, 0].set_title('Accuracy')
        axes[0, 0].legend()
        
        # Loss
        axes[0, 1].plot(history.history['loss'], label='Training')
        axes[0, 1].plot(history.history['val_loss'], label='Validation')
        axes[0, 1].set_title('Loss')
        axes[0, 1].legend()
        
        # Precision
        axes[0, 2].plot(history.history['precision'], label='Training')
        axes[0, 2].plot(history.history['val_precision'], label='Validation')
        axes[0, 2].set_title('Precision')
        axes[0, 2].legend()
        
        # Recall
        axes[1, 0].plot(history.history['recall'], label='Training')
        axes[1, 0].plot(history.history['val_recall'], label='Validation')
        axes[1, 0].set_title('Recall')
        axes[1, 0].legend()
        
        # Learning Rate
        if 'lr' in history.history:
            axes[1, 1].plot(history.history['lr'])
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].set_yscale('log')
        
        # F1 Score (calculated)
        train_f1 = 2 * (np.array(history.history['precision']) * np.array(history.history['recall'])) / \
                   (np.array(history.history['precision']) + np.array(history.history['recall']))
        val_f1 = 2 * (np.array(history.history['val_precision']) * np.array(history.history['val_recall'])) / \
                  (np.array(history.history['val_precision']) + np.array(history.history['val_recall']))
        
        axes[1, 2].plot(train_f1, label='Training F1')
        axes[1, 2].plot(val_f1, label='Validation F1')
        axes[1, 2].set_title('F1 Score')
        axes[1, 2].legend()
        
        plt.tight_layout()
        plt.savefig(f'evaluation_results/advanced_{model_name}_training.png', dpi=300, bbox_inches='tight')
        print(f"📊 Training plots saved: evaluation_results/advanced_{model_name}_training.png")

def main():
    """Main training function"""
    
    # Create trainer
    trainer = AdvancedFootDeformityTrainer()
    
    # Train different models
    models_to_train = ["efficientnet", "resnet", "densenet"]
    
    for model_name in models_to_train:
        print(f"\n{'='*60}")
        print(f"TRAINING {model_name.upper()} MODEL")
        print(f"{'='*60}")
        
        try:
            history = trainer.train_advanced_model(model_name, epochs=25)
            trainer.plot_training_results(history, model_name)
            
            print(f"✅ {model_name} training completed successfully!")
            
        except Exception as e:
            print(f"❌ Error training {model_name}: {e}")
            continue
    
    print("\n🎉 ADVANCED TRAINING COMPLETE!")
    print("Models saved in saved_models/ directory")

if __name__ == "__main__":
    main()
