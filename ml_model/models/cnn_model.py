"""
CNN Model Architecture for Foot Deformity Classification

This module defines the CNN architecture for classifying foot deformities
into three categories: normal, flatfoot, and hallux_valgus.
"""

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, models
import numpy as np

class FootDeformityCNN:
    def __init__(self, input_shape=(224, 224, 3), num_classes=3):
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None
        self.class_names = ['normal', 'flatfoot', 'hallux_valgus']
        
    def create_model(self, model_type='custom'):
        """Create the CNN model architecture."""
        if model_type == 'custom':
            self.model = self._create_custom_cnn()
        elif model_type == 'transfer_learning':
            self.model = self._create_transfer_learning_model()
        else:
            raise ValueError("model_type must be 'custom' or 'transfer_learning'")
        
        return self.model
    
    def _create_custom_cnn(self):
        """Create a custom CNN architecture optimized for foot deformity classification."""
        model = models.Sequential([
            # Input layer
            layers.Input(shape=self.input_shape),
            
            # First convolutional block
            layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Second convolutional block
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Third convolutional block
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Fourth convolutional block
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            # Global average pooling instead of flatten to reduce parameters
            layers.GlobalAveragePooling2D(),
            
            # Dense layers
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            # Output layer
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        return model
    
    def _create_transfer_learning_model(self):
        """Create a model using transfer learning with a pre-trained base."""
        # Use MobileNetV2 as base model (good for medical images)
        base_model = keras.applications.MobileNetV2(
            weights='imagenet',
            include_top=False,
            input_shape=self.input_shape
        )
        
        # Freeze the base model
        base_model.trainable = False
        
        model = models.Sequential([
            base_model,
            layers.GlobalAveragePooling2D(),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            layers.Dense(self.num_classes, activation='softmax')
        ])
        
        return model
    
    def compile_model(self, learning_rate=0.001, metrics=None):
        """Compile the model with optimizer, loss, and metrics."""
        if metrics is None:
            metrics = ['accuracy', 'precision', 'recall']
        
        optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        
        self.model.compile(
            optimizer=optimizer,
            loss='categorical_crossentropy',
            metrics=metrics
        )
        
        return self.model
    
    def get_model_summary(self):
        """Get model summary and parameter count."""
        if self.model is None:
            return "Model not created yet. Call create_model() first."
        
        return self.model.summary()
    
    def save_model(self, filepath):
        """Save the trained model."""
        if self.model is None:
            raise ValueError("Model not created yet.")
        
        self.model.save(filepath)
        print(f"Model saved to: {filepath}")
    
    def load_model(self, filepath):
        """Load a pre-trained model."""
        self.model = keras.models.load_model(filepath)
        print(f"Model loaded from: {filepath}")
        return self.model
    
    def predict(self, images):
        """Make predictions on new images."""
        if self.model is None:
            raise ValueError("Model not created or loaded yet.")
        
        predictions = self.model.predict(images)
        return predictions
    
    def predict_with_confidence(self, images):
        """Make predictions with confidence scores."""
        predictions = self.predict(images)
        
        results = []
        for pred in predictions:
            class_idx = np.argmax(pred)
            confidence = pred[class_idx]
            class_name = self.class_names[class_idx]
            
            results.append({
                'class': class_name,
                'confidence': float(confidence),
                'probabilities': {
                    self.class_names[i]: float(pred[i]) 
                    for i in range(len(self.class_names))
                }
            })
        
        return results

def create_data_generators(dataset_path, batch_size=32, validation_split=0.2):
    """Create data generators for training and validation."""
    
    # Data augmentation for training
    train_datagen = keras.preprocessing.image.ImageDataGenerator(
        rescale=1./255,
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        fill_mode='nearest',
        validation_split=validation_split
    )
    
    # Only rescaling for validation
    validation_datagen = keras.preprocessing.image.ImageDataGenerator(
        rescale=1./255,
        validation_split=validation_split
    )
    
    # Training generator
    train_generator = train_datagen.flow_from_directory(
        dataset_path,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        subset='training',
        shuffle=True
    )
    
    # Validation generator
    validation_generator = validation_datagen.flow_from_directory(
        dataset_path,
        target_size=(224, 224),
        batch_size=batch_size,
        class_mode='categorical',
        subset='validation',
        shuffle=False
    )
    
    return train_generator, validation_generator

def create_callbacks(model_save_path="ml_model/saved_models/best_model.keras"):
    """Create training callbacks."""

    # Custom callback to save model when training accuracy reaches good levels
    class SaveOnGoodAccuracy(keras.callbacks.Callback):
        def __init__(self, target_accuracy=0.85, save_path=None):
            super().__init__()
            self.target_accuracy = target_accuracy
            self.save_path = save_path or model_save_path
            self.saved = False

        def on_epoch_end(self, epoch, logs=None):
            current_accuracy = logs.get('accuracy', 0)
            if current_accuracy >= self.target_accuracy and not self.saved:
                print(f"\n🎯 Target accuracy {self.target_accuracy:.1%} reached! Saving model...")
                self.model.save(self.save_path)
                print(f"✅ Model saved to {self.save_path}")
                self.saved = True

                # Also save a backup with epoch number
                import os
                save_dir = os.path.dirname(self.save_path)
                backup_path = os.path.join(save_dir, f'model_epoch_{epoch+1}_acc_{current_accuracy:.3f}.keras')
                self.model.save(backup_path)
                print(f"✅ Backup saved to {backup_path}")

    callbacks = [
        # Custom callback to save when training accuracy is good
        SaveOnGoodAccuracy(target_accuracy=0.85),

        # Save best model based on training accuracy (since validation has issues)
        keras.callbacks.ModelCheckpoint(
            model_save_path.replace('.keras', '_checkpoint.keras'),
            monitor='accuracy',  # Monitor training accuracy instead of val_accuracy
            save_best_only=True,
            save_weights_only=False,
            mode='max',
            verbose=1
        ),

        # Reduce learning rate on plateau
        keras.callbacks.ReduceLROnPlateau(
            monitor='loss',  # Monitor training loss instead of val_loss
            factor=0.5,
            patience=5,
            min_lr=1e-7,
            verbose=1
        ),

        # Early stopping based on training metrics (since validation has issues)
        keras.callbacks.EarlyStopping(
            monitor='accuracy',  # Monitor training accuracy
            patience=15,  # Increased patience
            restore_best_weights=True,
            verbose=1,
            mode='max'
        ),

        # CSV logger
        keras.callbacks.CSVLogger(
            'ml_model/training/training_log.csv',
            append=True
        )
    ]

    return callbacks

if __name__ == "__main__":
    # Example usage
    print("Creating Foot Deformity CNN Model...")
    
    # Create model
    cnn = FootDeformityCNN()
    model = cnn.create_model(model_type='custom')
    cnn.compile_model()
    
    print("\nModel Summary:")
    cnn.get_model_summary()
    
    print(f"\nModel created with {model.count_params():,} parameters")
    print("Ready for training!")
